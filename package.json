{"name": "dify-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@arco-design/color": "^0.4.0", "@iconify/vue": "^4.1.1", "@vueuse/core": "^12.5.0", "axios": "^1.7.2", "cropperjs": "^2.0.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.0.34", "pinia-plugin-persistedstate": "^4.2.0", "seemly": "^0.3.10", "vue": "^3.5.13", "vue-draggable-next": "^2.2.1", "vue-router": "^4.4.0", "markdown-it": "^14.1.0", "highlight.js": "^11.11.1", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.19", "glob": "^11.0.1", "naive-ui": "^2.41.0", "postcss": "^8.4.38", "sass": "^1.77.4", "tailwindcss": "^3.4.4", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.27.2", "vite": "^6.2.0", "vite-plugin-jump-code": "^1.2.0"}}