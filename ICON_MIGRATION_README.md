# 图标本地化迁移完成

## 修改概述

已成功将项目中的所有在线 iconify 图标转换为本地资源，解决内网环境下图标无法加载的问题。

## 主要变更

### 1. 新增文件

- `scripts/download-icons.js` - 图标下载脚本
- `scripts/copy-icons-build.js` - 构建时图标复制脚本
- `src/utils/iconLoader.js` - 图标加载器和缓存管理
- `src/assets/icons/` - 本地图标存储目录（58个图标）
- `public/icons/` - 运行时图标访问目录
- `src/views/icon-test.vue` - 图标测试页面
- `docs/ICON_GUIDE.md` - 详细使用指南

### 2. 修改文件

- `src/components/common/SvgIcon/index.vue` - 修改为优先使用本地图标
- `src/main.js` - 添加图标预加载
- `src/router/basic-routes.js` - 添加图标测试路由
- `vite.config.js` - 添加构建时图标处理
- `package.json` - 添加图标相关脚本

## 工作原理

1. **预加载机制**: 应用启动时预加载所有本地图标到内存
2. **智能回退**: 优先使用本地图标，不存在时回退到 iconify
3. **构建集成**: 构建时自动复制图标文件到输出目录

## 使用方法

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 访问图标测试页面
http://localhost:8020/icon-test
```

### 添加新图标
```bash
# 下载/更新图标
npm run build:icons
```

### 生产构建
```bash
# 构建项目（自动处理图标）
npm run build
```

## 验证方法

1. 启动项目后访问 `/icon-test` 页面
2. 查看缓存统计信息
3. 检查浏览器网络面板，确认不再请求 iconify API
4. 在内网环境下测试图标显示

## 已包含的图标

项目包含 58 个本地图标，涵盖以下图标集：
- ri (Remix Icon)
- mdi (Material Design Icons)  
- carbon (Carbon Design System)
- material-symbols (Material Symbols)
- akar-icons, lucide, mingcute 等

## 注意事项

- 图标文件已添加到版本控制中
- 构建输出会自动包含图标文件
- 支持动态图标切换
- 完全兼容原有 iconify 语法

## 性能影响

- 增加约 200KB 的图标文件
- 预加载时间约 1-2 秒
- 后续图标访问无网络请求
- 整体性能提升（无网络延迟）

详细使用说明请参考 `docs/ICON_GUIDE.md`
