# 关键词匹配模块使用指南

## 概述

关键词匹配模块 (`src/utils/keywordMatcher.js`) 是一个独立的 JavaScript 模块，用于处理聊天系统中的特定关键词自动回复功能。该模块已从原有的聊天组件中提取出来，实现了功能的模块化和可维护性。

## 主要特性

- ✅ **独立模块**: 完全独立的 JS 文件，不依赖 Vue 或其他框架
- ✅ **功能开关**: 支持通过配置启用/禁用关键词匹配功能
- ✅ **灵活配置**: 支持动态添加、删除关键词配置
- ✅ **类型安全**: 包含完整的参数验证和错误处理
- ✅ **易于测试**: 提供独立的测试文件
- ✅ **向后兼容**: 保持原有聊天功能完全不变

## 文件结构

```
src/utils/
├── keywordMatcher.js      # 主模块文件
└── keywordMatcher.test.js # 测试文件

src/views/knowledge/chat/
└── index.vue              # 聊天组件（已重构使用新模块）
```

## 核心 API

### 主要函数

#### `processKeywordMessage(message)`
主要的处理函数，这是外部调用的主要接口。

```javascript
import { processKeywordMessage } from '@/utils/keywordMatcher'

const result = processKeywordMessage('用户输入的消息')
if (result) {
  if (result.type === 'disposal') {
    // 处理处置消息
    handleDisposalMessage(result.message)
  } else if (result.type === 'keyword') {
    // 处理关键词匹配
    handleKeywordResponse(result.message, result.config)
  }
}
```

#### `matchKeywords(message)`
检测消息是否匹配关键词。

```javascript
const config = matchKeywords('4A系统所有欧拉操作系统的主机和数据库主机要做升级改造')
if (config) {
  console.log('匹配的回复:', config.response)
  console.log('是否需要AI处理:', config.needsAIProcessing)
}
```

#### `isDisposalMessage(message)`
检测是否为处置消息。

```javascript
const isDisposal = isDisposalMessage('处置：这是一个处置消息')
// 返回: true
```

### 配置管理

#### `getKeywordConfigs()`
获取所有关键词配置。

```javascript
const configs = getKeywordConfigs()
console.log('当前配置数量:', configs.length)
```

#### `addKeywordConfig(config)`
添加新的关键词配置。

```javascript
addKeywordConfig({
  keywords: ['新关键词1', '新关键词2'],
  response: '这是自动回复内容',
  needsAIProcessing: false // 可选，默认 false
})
```

#### `removeKeywordConfig(index)`
移除指定索引的配置。

```javascript
removeKeywordConfig(0) // 移除第一个配置
```

### 功能控制

#### `isKeywordMatchingEnabled()`
检查关键词匹配功能是否启用。

```javascript
if (isKeywordMatchingEnabled()) {
  // 执行关键词匹配逻辑
}
```

## 使用示例

### 在聊天组件中使用

```javascript
// 在 Vue 组件中
import { processKeywordMessage } from '@/utils/keywordMatcher'

const handleSend = async () => {
  const message = inputMessage.value.trim()
  if (!message) return

  // 使用关键词匹配模块
  const keywordResult = processKeywordMessage(message)
  if (keywordResult) {
    if (keywordResult.type === 'disposal') {
      handleDisposalMessage(message)
      return
    } else if (keywordResult.type === 'keyword') {
      handleKeywordResponse(message, keywordResult.config)
      return
    }
  }

  // 继续正常的消息处理流程
  // ...
}
```

### 动态管理关键词

```javascript
import { 
  addKeywordConfig, 
  removeKeywordConfig, 
  getKeywordConfigs 
} from '@/utils/keywordMatcher'

// 添加新的关键词配置
addKeywordConfig({
  keywords: ['帮助', 'help'],
  response: '这是帮助信息...',
  needsAIProcessing: false
})

// 查看所有配置
const configs = getKeywordConfigs()
console.log('当前配置:', configs)

// 移除配置
removeKeywordConfig(configs.length - 1) // 移除最后一个
```

## 配置格式

每个关键词配置对象包含以下字段：

```javascript
{
  keywords: ['关键词1', '关键词2'],        // 必需：关键词数组
  response: '自动回复的内容',              // 必需：回复内容
  needsAIProcessing: false                // 可选：是否需要AI进一步处理
}
```

## 测试

### 运行测试

在浏览器控制台中运行：

```javascript
// 导入测试模块
import { runAllTests } from '@/utils/keywordMatcher.test.js'

// 运行所有测试
runAllTests()
```

或者直接在控制台调用：

```javascript
window.testKeywordMatcher()
```

### 测试覆盖

- ✅ 关键词匹配功能
- ✅ 处置消息检测
- ✅ 主处理函数
- ✅ 配置管理
- ✅ 功能开关

## 功能开关

可以通过以下方式禁用关键词匹配功能：

```javascript
// 在全局设置中
window.DISABLE_KEYWORD_MATCHING = true

// 或者修改 keywordMatcher.js 中的 isKeywordMatchingEnabled 函数
```

## 迁移说明

### 从原有代码迁移

原有的聊天组件中的关键词匹配逻辑已经完全迁移到独立模块中：

- ✅ `keywordResponses` 配置数组 → `keywordMatcher.js`
- ✅ `isDisposalMessage` 函数 → `keywordMatcher.js`
- ✅ `matchKeywords` 函数 → `keywordMatcher.js`
- ✅ 主处理逻辑 → `processKeywordMessage` 函数

### 兼容性

- ✅ 保持所有原有功能不变
- ✅ 支持所有原有的关键词和回复
- ✅ 保持相同的处理流程和用户体验

## 最佳实践

1. **模块化使用**: 只导入需要的函数，避免导入整个模块
2. **错误处理**: 始终检查函数返回值，处理可能的错误情况
3. **配置验证**: 添加新配置时使用 `addKeywordConfig` 而不是直接修改数组
4. **功能测试**: 在生产环境部署前运行测试确保功能正常
5. **性能考虑**: 关键词匹配是同步操作，避免在关键词数组中添加过多项目

## 故障排除

### 常见问题

1. **关键词不匹配**: 检查关键词是否完全包含在用户消息中
2. **功能不生效**: 确认 `isKeywordMatchingEnabled()` 返回 `true`
3. **配置添加失败**: 检查配置格式是否正确，使用 `addKeywordConfig` 函数

### 调试技巧

```javascript
// 启用调试日志
console.log('关键词匹配结果:', processKeywordMessage(message))

// 检查当前配置
console.log('当前配置:', getKeywordConfigs())

// 检查功能状态
console.log('功能启用状态:', isKeywordMatchingEnabled())
```
