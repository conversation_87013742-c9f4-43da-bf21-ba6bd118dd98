# 本地图标系统使用指南

## 概述

本项目已将所有在线 iconify 图标转换为本地资源，以支持内网环境下的正常使用。系统会优先加载本地 SVG 图标，如果本地不存在则回退到 iconify 在线服务。

## 目录结构

```
src/assets/icons/           # 本地图标存储目录
├── akar-icons/            # 按图标集分类
│   └── plus.svg
├── mdi/
│   ├── company.svg
│   ├── logout.svg
│   └── report-bar.svg
├── ri/
│   ├── admin-line.svg
│   ├── chat-ai-line.svg
│   └── reset-left-line.svg
├── ...                    # 其他图标集
└── icon-map.json          # 图标映射文件

public/icons/              # 运行时访问的图标文件（从 src/assets/icons 复制）
scripts/
├── download-icons.js      # 图标下载脚本
└── copy-icons-build.js    # 构建时复制脚本
```

## 使用方法

### 1. 基本使用

```vue
<template>
  <!-- 使用本地图标 -->
  <SvgIcon icon="ri:chat-ai-line" />
  <SvgIcon icon="mdi:company" />
  <SvgIcon icon="akar-icons:plus" />
</template>
```

### 2. 图标测试页面

访问 `/icon-test` 路由可以查看所有可用图标和缓存状态。

## 添加新图标

### 方法一：修改下载脚本（推荐）

1. 编辑 `scripts/download-icons.js`
2. 在 `icons` 数组中添加新的图标名称：
   ```javascript
   const icons = [
     // 现有图标...
     'new-collection:new-icon-name',  // 添加新图标
   ]
   ```
3. 运行下载脚本：
   ```bash
   npm run build:icons
   ```

### 方法二：手动添加

1. 从 [Iconify](https://iconify.design/) 下载 SVG 文件
2. 按图标集分类放入 `src/assets/icons/` 对应目录
3. 更新 `src/assets/icons/icon-map.json` 文件
4. 复制到 `public/icons/` 目录

## 开发命令

```bash
# 开发模式
npm run dev

# 下载/更新图标
npm run build:icons

# 构建项目（会自动复制图标到输出目录）
npm run build

# 预览构建结果
npm run preview
```

## 工作原理

### 1. 图标预加载

应用启动时，`iconLoader.js` 会预加载所有本地图标到内存缓存中，提高访问速度。

### 2. SvgIcon 组件

- 优先检查本地缓存
- 如果本地存在，直接使用 SVG 内容
- 如果本地不存在，回退到 iconify 在线服务

### 3. 构建时处理

构建时会自动将 `src/assets/icons/` 复制到 `dist/icons/`，确保生产环境可用。

## 故障排除

### 图标不显示

1. 检查图标名称是否正确
2. 访问 `/icon-test` 查看缓存状态
3. 检查浏览器控制台是否有错误信息
4. 确认图标文件是否存在于 `public/icons/` 目录

### 新图标未生效

1. 确认已运行 `npm run build:icons`
2. 检查 `icon-map.json` 是否已更新
3. 清除浏览器缓存
4. 重启开发服务器

### 内网环境部署

1. 确保 `dist/icons/` 目录包含所有图标文件
2. 检查服务器配置，确保可以访问静态资源
3. 如果使用 CDN，确保图标文件也被上传

## 性能优化

- 图标预加载采用批量加载，避免过多并发请求
- 使用内存缓存，避免重复网络请求
- SVG 文件较小，对性能影响最小

## 维护建议

1. 定期检查是否有新的图标需求
2. 清理不再使用的图标文件
3. 保持 `icon-map.json` 与实际文件同步
4. 在版本控制中包含所有图标文件

## 技术细节

- 使用 Fetch API 加载 SVG 文件
- 支持动态图标切换
- 兼容原有 iconify 语法
- 支持所有 CSS 样式和属性
