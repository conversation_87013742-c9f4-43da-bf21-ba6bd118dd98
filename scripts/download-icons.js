import fs from 'fs'
import path from 'path'
import https from 'https'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 项目中使用的所有图标
const icons = [
  'akar-icons:plus',
  'bxs:lock',
  'carbon:document-requirements',
  'carbon:location-company',
  'carbon:user-role',
  'codex:file',
  'fa-solid:sort',
  'fluent-mdl2:join-online-meeting',
  'fluent:button-20-regular',
  'fluent:navigation-32-regular',
  'fluent:organization-16-regular',
  'fluent:text-change-reject-20-filled',
  'hugeicons:workflow-square-03',
  'ic:round-upload',
  'icon-park-outline:preview-open',
  'icon-park-outline:switch',
  'icon-park-solid:category-management',
  'iconoir:password-check',
  'ix:details',
  'ix:drag-gripper',
  'lets-icons:favorites-duotone',
  'lucide:search',
  'mage:robot',
  'material-symbols-light:key-rounded',
  'material-symbols:add',
  'material-symbols:arrow-back',
  'material-symbols:chat',
  'material-symbols:chevron-left',
  'material-symbols:chevron-right',
  'material-symbols:cloud-upload',
  'material-symbols:content-copy',
  'material-symbols:delete',
  'material-symbols:description',
  'material-symbols:download',
  'material-symbols:edit',
  'material-symbols:help-outline',
  'material-symbols:person',
  'material-symbols:publish',
  'material-symbols:search',
  'material-symbols:send',
  'material-symbols:smart-toy',
  'material-symbols:stop',
  'mdi:company',
  'mdi:logout',
  'mdi:report-bar',
  'mi:delete',
  'mingcute:edit-line',
  'mynaui:star-solid',
  'oui:arrow-down',
  'ri:admin-line',
  'ri:chat-ai-line',
  'ri:reset-left-line',
  'solar:user-linear',
  'streamline:dictionary-language-book',
  'tabler:user-filled',
  'typcn:link-outline',
  'uil:export',
  'uil:import'
]

// 创建目录结构
const iconsDir = path.join(__dirname, '../src/assets/icons')
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true })
}

// 下载单个图标
async function downloadIcon(iconName) {
  const [collection, name] = iconName.split(':')
  const collectionDir = path.join(iconsDir, collection)
  
  if (!fs.existsSync(collectionDir)) {
    fs.mkdirSync(collectionDir, { recursive: true })
  }
  
  const filePath = path.join(collectionDir, `${name}.svg`)
  
  // 如果文件已存在，跳过下载
  if (fs.existsSync(filePath)) {
    console.log(`✓ ${iconName} already exists`)
    return
  }
  
  const url = `https://api.iconify.design/${collection}/${name}.svg`
  
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        const fileStream = fs.createWriteStream(filePath)
        response.pipe(fileStream)
        fileStream.on('finish', () => {
          fileStream.close()
          console.log(`✓ Downloaded ${iconName}`)
          resolve()
        })
      } else {
        console.error(`✗ Failed to download ${iconName}: ${response.statusCode}`)
        reject(new Error(`HTTP ${response.statusCode}`))
      }
    }).on('error', (err) => {
      console.error(`✗ Error downloading ${iconName}:`, err.message)
      reject(err)
    })
  })
}

// 批量下载图标
async function downloadAllIcons() {
  console.log(`Starting download of ${icons.length} icons...`)
  
  const batchSize = 5 // 限制并发数量
  for (let i = 0; i < icons.length; i += batchSize) {
    const batch = icons.slice(i, i + batchSize)
    await Promise.allSettled(batch.map(downloadIcon))
    
    // 添加延迟避免请求过于频繁
    if (i + batchSize < icons.length) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  console.log('Download completed!')
}

// 生成图标映射文件
function generateIconMap() {
  const iconMap = {}
  
  icons.forEach(iconName => {
    const [collection, name] = iconName.split(':')
    const filePath = path.join(iconsDir, collection, `${name}.svg`)
    
    if (fs.existsSync(filePath)) {
      iconMap[iconName] = `/src/assets/icons/${collection}/${name}.svg`
    }
  })
  
  const mapFilePath = path.join(iconsDir, 'icon-map.json')
  fs.writeFileSync(mapFilePath, JSON.stringify(iconMap, null, 2))
  console.log(`Generated icon map with ${Object.keys(iconMap).length} icons`)
}

// 执行下载
downloadAllIcons()
  .then(() => {
    generateIconMap()
  })
  .catch(console.error)
