import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 递归复制目录
function copyDir(src, dest) {
  if (!fs.existsSync(src)) {
    console.warn(`源目录不存在: ${src}`)
    return
  }

  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }

  const entries = fs.readdirSync(src, { withFileTypes: true })

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name)
    const destPath = path.join(dest, entry.name)

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  }
}

// 复制图标文件到构建输出目录
function copyIconsToBuild() {
  const iconsSource = path.join(__dirname, '../src/assets/icons')
  const iconsTarget = path.join(__dirname, '../dist/icons')

  console.log('开始复制图标文件...')
  console.log(`源目录: ${iconsSource}`)
  console.log(`目标目录: ${iconsTarget}`)

  try {
    copyDir(iconsSource, iconsTarget)
    console.log('图标文件复制完成!')
    
    // 统计复制的文件数量
    const countFiles = (dir) => {
      let count = 0
      const entries = fs.readdirSync(dir, { withFileTypes: true })
      for (const entry of entries) {
        if (entry.isDirectory()) {
          count += countFiles(path.join(dir, entry.name))
        } else if (entry.name.endsWith('.svg')) {
          count++
        }
      }
      return count
    }
    
    const fileCount = countFiles(iconsTarget)
    console.log(`共复制了 ${fileCount} 个 SVG 图标文件`)
    
  } catch (error) {
    console.error('复制图标文件时出错:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  copyIconsToBuild()
}

export { copyIconsToBuild }
