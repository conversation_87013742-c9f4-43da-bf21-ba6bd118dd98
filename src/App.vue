<script setup>
import { NConfigProvider, zhCN, dateZhC<PERSON> } from "naive-ui"
import { useAppStore, useTabStore, useAuthStore } from '@/store'
// import NaiveProvider from "@/components/common/NaiveProvider/index.vue"
import { useTheme } from "@/hooks/useTheme"
import LoadingFull from "@/components/loading-full/index.vue"
import { RouterView } from "vue-router"
import { onMounted } from 'vue'
import api from '@/api'

const { theme, themeOverrides } = useTheme()

const layouts = new Map()
function getLayout(name) {
  // 利用map将加载过的layout缓存起来，防止重新加载layout导致页面闪烁
  if (layouts.get(name))
    return layouts.get(name)
  const layout = markRaw(defineAsyncComponent(() => import(`@/layout/index.vue`)))
  layouts.set(name, layout)
  return layout
}

const route = useRoute()
const appStore = useAppStore()
// if (appStore.layout === 'default')
//   appStore.setLayout('')
const Layout = computed(() => {
  if (!route.matched?.length)
    return null
  return getLayout()
})

const tabStore = useTabStore()
const keepAliveNames = computed(() => {
  return tabStore.tabs.filter(item => item.keepAlive).map(item => item.name)
})

watchEffect(() => {
  // appStore.setThemeColor(appStore.primaryColor, appStore.isDark)
})
onMounted(() => {
  // 1h 后更新token
  setInterval(async () => {
    const res = await api.refresh_token()
    useAuthStore().setToken(res.data)
  }, 60 * 60 * 1000)
})
</script>

<template>
  <n-config-provider class="h-full w-full app-container" :theme="theme" :theme-overrides="themeOverrides" :locale="zhCN"
    :date-locale="dateZhCN">
    <!-- <router-view v-if="Layout" v-slot="{ Component, route: curRoute }">
      <LayoutSetting v-if="layoutSettingVisible" class="fixed right-12 top-1/2 z-999" />
    </router-view> -->
    <component :is="Layout">
      <transition name="fade-slide" mode="out-in" appear>
        <RouterView></RouterView>
      </transition>
    </component>
    <LoadingFull></LoadingFull>
  </n-config-provider>
</template>

<style scoped>
.app-container {
  background-color: var(--chatfire_bg_common);
}
</style>
