<!-- 实现一个菜单，左侧名称右侧箭头 -->
<template>
  <div @click="emit('select', item)"
    class="menu-item flex justify-between items-center pl-8 pr-[18px] h-[42px] cursor-pointer hover:bg-[#F3F3F5] mt-1 mx-2 rounded-sm text-sm"
    :class="{ active: item.active, 'pl-[14px] pr-[14px]': collapsed }">
    <div class="menu-item-left flex gap-2 " :style="'padding-left: ' + (deep * 30) + 'px'">
      <!-- 折叠需要单独处理 -->
      <template v-if="collapsed">
        <template v-if="dropdownOptions?.length > 0">
          <div v-if="!isSub"
            class="flex items-center justify-center w-5 h-5 text-xs text-[#ffffff] rounded-full bg-[#749BFF] shrink-0">
            {{
              item.name?.substring(0, 1) }}</div>
        </template>
        <template v-else>
          <div v-if="!isSub"
            class="flex items-center justify-center w-5 h-5 text-xs text-[#ffffff] rounded-full bg-[#749BFF] shrink-0">
            <SvgIcon v-if="item.iconName" :icon="item.iconName" :width="20" />
            <span v-else>{{
              item.name?.substring(0, 1) }}</span>
          </div>
          <!-- <div v-else="item.iconName" class="flex items-center justify-center w-5 h-5 shrink-0">
            <SvgIcon :icon="item.iconName" :width="20" />
          </div> -->
        </template>
      </template>
      <template v-else>
        <div v-if="item.iconName" class="flex items-center justify-center w-5 h-5">
          <SvgIcon :icon="item.iconName" :width="20" />
        </div>
        <div v-else-if="!isSub && !collapsed"
          class="flex items-center justify-center w-5 h-5 text-xs text-[#ffffff] rounded-full bg-[#749BFF] shrink-0">{{
            item.name?.substring(0, 1) }}</div>
      </template>
      <span class="left-text shrink-0" :class="{ collapsed: collapsed }">{{ item.name }}</span>
    </div>

    <div v-if="item.children?.length > 0" class="menu-item-right h-full  flex items-center justify-center"
      @click.stop="toggleCollapse(item)">
      <SvgIcon
        :class="{ 'rotate-180': item.isCollapse, 'rotate-0': !item.isCollapse, 'transition-all duration-300 ease-in-out': true }"
        icon="oui:arrow-down" :width="15" />
    </div>
  </div>
  <!-- 子集 -->
  <div
    :class="{ 'max-h-[1000px]': item.isCollapse, 'max-h-0': !item.isCollapse, 'overflow-hidden': item.children?.length > 0, 'transition-all duration-300 ease-in-out': true }">
    <SubMenu v-for="(subItem, index) in item.children" :key="index" :item="subItem" :deep="deep + 1" :is-sub="true"
      :collapsed="collapsed" @select="emit('select', subItem)">
    </SubMenu>
  </div>
  <!-- </transition> -->
</template>
<script setup>
import { computed } from 'vue';
import SubMenu from './Item.vue';

const emit = defineEmits(['select']);
const props = defineProps({
  isSub: {
    type: Boolean,
    default: false,
  },
  deep: {
    type: Number,
    default: 0,
  },
  item: {
    type: Object,
    default: () => { },
  },
  collapsed: {
    type: Boolean,
    default: false,
  }
});

const dropdownOptions = computed(() => {
  return props.item.children?.map(item => {
    return {
      label: item.name,
      key: item.id,
      icon: item.icon,
      onClick: () => handleSelect(item),
      children: item.children?.map(subItem => {
        return {
          label: subItem.name,
          key: subItem.id,
          icon: subItem.icon,
          onClick: () => handleSelect(subItem),
        }
      })
    }
  })
})

const toggleCollapse = (item) => {
  item.isCollapse = !item.isCollapse;
};

const handleSelect = (key, item) => {
}
</script>
<style lang="scss" scoped>
.active {
  color: #467bff;
  background: #ECF1FF;
}

.menu-item {
  .left-text {
    opacity: 1;
    transition: opacity 0.3s ease;

    &.collapsed {
      opacity: 0;
    }
  }
}
</style>