<!-- 实现一个菜单，左侧名称右侧箭头 -->
<template>
  <div class="menu flex flex-col w-full">
    <SubMenu v-for="item in data" :item="item" :collapsed="collapsed" @select="selectMenu"></SubMenu>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import SubMenu from './Item.vue';
import { useAgentStore } from "@/store/modules/agent";
import { treeTraverse } from "@/utils/tools";
const agentStore = useAgentStore()

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: () => [],
  },
})

watch(() => props.collapsed, (newVal) => {
  // 需要采集所有子集
  if (newVal) {
    treeTraverse(props.data, (item) => {
      item.isCollapse = false;
    });
  }
})

const selectMenu = (item) => {
  // 置空
  agentStore.setCurrentAgent(undefined)
  // 需要采集所有子集
  const ids = []
  treeTraverse(props.data, (item) => {
    item.active = false;
  });
  treeTraverse([item], (subItem) => {
    // 先排序
    if (subItem.agents?.length > 0) {
      const subItemIds = subItem.agents.sort((a, b) => a.orderNum - b.orderNum).map(t => t.id)
      subItem.agentIds && ids.push(...subItemIds);
    }
  });
  item.active = true;
  agentStore.selectAgentMenu({ key: item.key, agentIds: ids })
};
</script>
<style lang="scss" scoped></style>