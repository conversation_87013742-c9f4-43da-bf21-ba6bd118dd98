<template>
    <div>
        <n-collapse v-model:expanded-names="activeKey">
            <n-collapse-item name="1">
                <template #header>
                  <div v-if="loading">
                      思考中
                  </div>
                  <div v-else-if="thinkContent">
                      已深度思考
                  </div>
                  <div v-else>
                      思考中
                  </div>
                </template>
                 <!-- <p class="think-content">
                  {{ thinkContent }}
                </p> -->

                <div class="think-content" v-if="thinkContent">
                    <MD :data="thinkContent"></MD>
                </div>
                <div class="think-content" v-else-if="loading">
                    <div style="color: var(--dify_text_secondary); font-style: italic;">正在思考...</div>
                </div>
            </n-collapse-item>
        </n-collapse>
            
        <div style="height: 12px;"></div>
        <div class="reply-content">
            <MD :data="replyContent" :references="references" v-show="replyContent"></MD>
        </div>

        <!-- 文档来源 -->
        <div v-if="docAggs && docAggs.length > 0" class="doc-sources">
          <div class="doc-sources-title">参考文档：</div>
          <div class="doc-sources-list">
            <span v-for="(doc, index) in docAggs" :key="doc.doc_id" class="doc-item">
              {{ doc.doc_name }}
              <span v-if="index < docAggs.length - 1" class="doc-separator">、</span>
            </span>
          </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import MD from './md.vue'

const props = defineProps({
  data: String,
  streamData: Object,
})

const activeKey = ref(['1'])
const END_TAG = '</think>'
const END_INDEX = computed(() => props.data.indexOf(END_TAG))

const originThinkContent = computed(() => {
  const index = END_INDEX.value
  if (index > -1) {
    return props.data.slice(0, index + END_TAG.length)
  }
  return props.data 
})

const thinkContent = computed(() => {
  if (!props.data) return ''

  // 清理所有think标签，并处理可能的多余空行
  const content = originThinkContent.value
    .replace(/<\/?think>/g, '')
    .replace(/<\/think>/g, '')
    .split('\n')
    .filter(line => line.trim())
    .join('\n')
    .trim()

  return content
})

const replyContent = computed(() => {
  if (!props.data || END_INDEX.value == -1) return ''

  const content = props.data.slice(END_INDEX.value + END_TAG.length)
    .replace(/<\/?think>/g, '')
    .trim()

  return content
})

const loading = computed(() => {
  if (!props.data) return false
  const thinkStart = props.data.includes('<think>')
  const thinkEnd = props.data.includes('</think>')
  return (thinkStart && !thinkEnd)
})

// 提取引用数据
const references = computed(() => {
  return props.streamData?.reference?.chunks || []
})

// 提取文档聚合数据
const docAggs = computed(() => {
  return props.streamData?.reference?.doc_aggs || []
})

watch(() => loading.value, (nVal) => {
  if (!nVal) {
    activeKey.value = []
  }
})

</script>

<style lang="scss" scoped>
.think-content {
  margin: 0;
  padding: 12px 16px;
  background-color: rgba(var(--dify_bg_secondary_rgb), 0.5);
  border-radius: 8px;
  
  :deep(.markdown-body) {
    color: var(--dify_text_secondary);
    font-size: 13px;
    
    p {
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    pre, code {
      background-color: rgba(var(--dify_bg_secondary_rgb), 0.8);
    }
    
    blockquote {
      border-left-color: var(--dify_border);
      color: var(--dify_text_secondary);
      background-color: rgba(var(--dify_bg_secondary_rgb), 0.3);
      padding: 8px 12px;
      margin: 8px 0;
    }
  }
}

.reply-content {
  :deep(.markdown-body) {
    color: var(--dify_text_primary);
    font-size: 14px;
    
    pre, code {
      background-color: rgba(var(--dify_bg_secondary_rgb), 0.8);
    }
    
    blockquote {
      border-left-color: var(--dify_color_primary);
      background-color: rgba(var(--dify_color_primary_rgb), 0.05);
      padding: 12px 16px;
      margin: 12px 0;
    }
  }
}

:deep(.n-collapse) {
  background-color: transparent;
  border: none;
  
  .n-collapse-item {
    .n-collapse-item__header {
      padding: 8px 0;
      font-size: 13px;
      color: var(--dify_text_secondary);
      
      &:hover {
        color: var(--dify_color_primary);
      }
    }
    
    .n-collapse-item__content-wrapper {
      padding: 0;
      
      .n-collapse-item__content-inner {
        padding: 8px 0;
      }
    }
  }
}

.doc-sources {
  margin-top: 16px;
  padding: 12px 16px;
  background-color: rgba(var(--dify_bg_secondary_rgb), 0.3);
  border-radius: 8px;
  border-left: 3px solid var(--dify_color_primary);

  .doc-sources-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--dify_text_secondary);
    margin-bottom: 8px;
  }

  .doc-sources-list {
    font-size: 13px;
    color: var(--dify_text_tertiary);
    line-height: 1.4;

    .doc-item {
      color: var(--dify_color_primary);
      font-weight: 500;
    }

    .doc-separator {
      color: var(--dify_text_tertiary);
      margin: 0 2px;
    }
  }
}
</style>