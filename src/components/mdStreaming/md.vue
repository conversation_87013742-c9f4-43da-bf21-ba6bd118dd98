<template>
  <div class="markdown-body" ref="markRef"></div>
</template>

<script setup>
import { onMounted, ref, watch, nextTick } from 'vue'
import markdownit from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/atom-one-dark.css'

const props = defineProps({
  data: String,
  references: {
    type: Array,
    default: () => []
  }
})

const markRef = ref()
const md = markdownit({
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {}
    }
    return '' // use external default escaping
  },
})

// 处理引用标记的函数
const processReferences = (content) => {
  if (!content || !props.references.length) return content

  // 替换 ##数字$$ 格式的引用标记
  return content.replace(/##(\d+)\$\$/g, (match, index) => {
    const refIndex = parseInt(index)
    const reference = props.references[refIndex]

    if (reference) {
      return `<span class="reference-marker" data-ref-index="${refIndex}">[${refIndex + 1}]</span>`
    }
    return match
  })
}

// 渲染内容的函数
const renderContent = () => {
  if (!props.data || !markRef.value) return

  const processedContent = processReferences(props.data)
  const renderedHTML = md.render(processedContent)

  // 只有当内容真正改变时才更新DOM
  if (markRef.value.innerHTML !== renderedHTML) {
    markRef.value.innerHTML = renderedHTML

    // 添加引用按钮的事件监听
    nextTick(() => {
      const referenceMarkers = markRef.value.querySelectorAll('.reference-marker')
      referenceMarkers.forEach(marker => {
        marker.addEventListener('mouseenter', handleReferenceHover)
        marker.addEventListener('mouseleave', handleReferenceLeave)
      })
    })
  }
}

// 处理引用悬停
const handleReferenceHover = (event) => {
  const refIndex = parseInt(event.target.dataset.refIndex)
  const reference = props.references[refIndex]

  if (reference) {
    showReferencePopover(event.target, reference)
  }
}

// 处理引用离开
const handleReferenceLeave = () => {
  hideReferencePopover()
}

// 显示引用弹窗
const showReferencePopover = (target, reference) => {
  // 移除已存在的弹窗
  hideReferencePopover()

  const popover = document.createElement('div')
  popover.className = 'reference-popover'
  popover.innerHTML = `
    <div class="reference-content">${reference.content}</div>
    <div class="reference-doc-name">${reference.document_name}</div>
  `

  document.body.appendChild(popover)

  // 定位弹窗
  const rect = target.getBoundingClientRect()
  popover.style.position = 'fixed'
  popover.style.left = rect.left + 'px'
  popover.style.top = (rect.bottom + 8) + 'px'
  popover.style.zIndex = '9999'
}

// 隐藏引用弹窗
const hideReferencePopover = () => {
  const existingPopover = document.querySelector('.reference-popover')
  if (existingPopover) {
    existingPopover.remove()
  }
}

onMounted(() => {
  renderContent()
})

watch(() => [props.data, props.references], () => {
  renderContent()
}, { deep: true })
</script>

<style lang="scss">
.markdown-body {
  color: var(--dify_text_primary, #17181a);
  font-size: 14px;
  line-height: 1.6;
  padding: 12px;

  h1, h2, h3, h4, h5, h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }

  h1 {
    font-size: 2em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid var(--divider-color);
  }

  h2 {
    font-size: 1.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid var(--divider-color);
  }

  h3 {
    font-size: 1.25em;
  }

  h4 {
    font-size: 1em;
  }

  h5 {
    font-size: 0.875em;
  }

  h6 {
    font-size: 0.85em;
    color: var(--text-color-3);
  }

  p {
    margin-top: 0;
    margin-bottom: 16px;
  }

  a {
    color: var(--primary-color);
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }

  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }

  del {
    text-decoration: line-through;
  }

  blockquote {
    margin: 0;
    padding: 0 1em;
    color: var(--text-color-3);
    border-left: 0.25em solid var(--divider-color);
  }

  ul, ol {
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 16px;
  }

  code {
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, monospace;
    font-size: 0.9em;
    padding: 0.2em 0.4em;
    margin: 0;
    background-color: var(--code-color);
    border-radius: 3px;
  }

  pre {
    margin: 16px 0;
    padding: 16px;
    overflow: auto;
    background-color: var(--code-color);
    border-radius: 6px;

    code {
      padding: 0;
      margin: 0;
      font-size: 100%;
      word-break: normal;
      white-space: pre;
      background: transparent;
      border: 0;
    }
  }

  img {
    max-width: 100%;
    height: auto;
    margin: 8px 0;
  }

  table {
    border-spacing: 0;
    border-collapse: collapse;
    margin: 16px 0;
    width: 100%;

    th, td {
      padding: 6px 13px;
      border: 1px solid var(--divider-color);
    }

    th {
      font-weight: 600;
      background-color: var(--table-header-color);
    }

    tr:nth-child(2n) {
      background-color: var(--table-striped-color);
    }
  }

  // 引用标记样式
  .reference-marker {
    display: inline-block;
    background-color: var(--dify_color_primary, #467bff);
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin: 0 2px;
    cursor: pointer;
    vertical-align: super;
    line-height: 1;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--dify_color_primary_dark, #0B57D8);
      transform: scale(1.1);
    }
  }
}

// 引用弹窗样式
.reference-popover {
  position: fixed;
  background: white;
  border: 1px solid var(--dify_border, #eeeef0);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  z-index: 9999;

  .reference-content {
    color: var(--dify_text_primary, #17181a);
    margin-bottom: 8px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .reference-doc-name {
    color: var(--dify_text_tertiary, #7c7d81);
    font-size: 12px;
    font-weight: 500;
    padding-top: 8px;
    border-top: 1px solid var(--dify_border, #eeeef0);
  }
}
</style>
