<template>
  <div class="hybrid-renderer" @click="handleGlobalClick">
    <!-- 思考内容 -->
    <n-collapse v-if="props.data && props.data.includes('<think>')" v-model:expanded-keys="activeKey" class="think-collapse" default-expanded-names="1">
      <n-collapse-item title="思考过程" name="1">
        <div class="think-content">
          <div class="markdown-body" v-html="renderedThinkContent"></div>
        </div>
      </n-collapse-item>
    </n-collapse>

    <div style="height: 12px;" v-if="props.data && props.data.includes('<think>')"></div>

    <!-- 回复内容 -->
    <div class="reply-content">
      <div class="markdown-body" v-html="renderedReplyContent" v-show="replyContent"></div>
    </div>

    <!-- 文档来源 -->
    <div v-if="docAggs && docAggs.length > 0" class="doc-sources">
      <div class="doc-sources-title">参考文档：</div>
      <div class="doc-sources-list">
        <span v-for="(doc, index) in docAggs" :key="doc.doc_id || doc.id || index" class="doc-item">
          {{ doc.doc_name || doc.name || doc.title || '未知文档' }}
          <span v-if="index < docAggs.length - 1" class="doc-separator">、</span>
        </span>
      </div>
    </div>

    <!-- 引用模态框 -->
    <n-modal v-model:show="showReferenceModal" preset="card" style="width: 600px;" title="引用内容">
      <div class="reference-modal-content">
        <div class="reference-content" v-html="formatReferenceContent(currentReference.content)"></div>
        <div class="reference-doc-name">来源：{{ currentReference.document_name }}</div>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { NModal, NCollapse, NCollapseItem } from 'naive-ui'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'

const props = defineProps({
  data: String,
  streamData: Object,
})

// markdown渲染器现在在计算属性中创建

const activeKey = ref(['1']) // 默认展开思考内容
const showReferenceModal = ref(false)
const currentReference = ref({})

const END_TAG = '</think>'
const END_INDEX = computed(() => props.data?.indexOf(END_TAG) || -1)



// 思考内容直接从props.data中提取

const thinkContent = computed(() => {
  if (!props.data) return ''

  // 调试信息


  // 直接从原始数据中提取思考内容
  let content = ''

  // 使用正则表达式提取<think>和</think>之间的内容
  const thinkMatch = props.data.match(/<think>([\s\S]*?)<\/think>/i)
  if (thinkMatch && thinkMatch[1]) {
    content = thinkMatch[1].trim()

  } else {
    // 如果没有完整的think标签，可能还在思考中
    const startMatch = props.data.match(/<think>([\s\S]*)/i)
    if (startMatch && startMatch[1]) {
      content = startMatch[1].trim()

    }
  }

  console.log('最终思考内容长度:', content.length)
  return content
})

// 渲染后的思考内容
const renderedThinkContent = computed(() => {
  if (!thinkContent.value) return ''

  // 使用markdown渲染器处理思考内容
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: function (str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang }).value
        } catch (__) {}
      }
      return ''
    }
  })

  let rendered = md.render(thinkContent.value)

  // 处理引用标记
  if (references.value.length > 0) {
    rendered = processReferences(rendered)
  }

  return rendered
})

// 渲染后的回复内容
const renderedReplyContent = computed(() => {
  if (!replyContent.value) return ''

  // 使用markdown渲染器处理回复内容
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: function (str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang }).value
        } catch (__) {}
      }
      return ''
    }
  })

  let rendered = md.render(replyContent.value)

  // 处理引用标记
  if (references.value.length > 0) {
    rendered = processReferences(rendered)
  }

  return rendered
})

// 回复内容
const replyContent = computed(() => {
  if (!props.data) return ''

  const index = END_INDEX.value


  if (index > -1) {
    // 有结束标签，提取回复部分
    const reply = props.data.slice(index + END_TAG.length).trim()

    return reply
  } else if (props.data.includes('<think>')) {
    // 有思考标签但没有结束标签，说明还在思考中，回复内容为空

    return ''
  } else {
    // 没有思考标签，整个内容都是回复内容

    return props.data
  }
})

// 思考面板始终保持展开状态

// 提取引用数据 - 支持多种数据结构
const references = computed(() => {
  if (!props.streamData) return []



  // 直接使用reference数组，因为我们现在直接存储chunks数组到reference字段
  let result = []
  if (Array.isArray(props.streamData.reference)) {
    result = props.streamData.reference
  } else if (props.streamData.reference?.chunks) {
    result = props.streamData.reference.chunks
  } else if (props.streamData.chunks) {
    result = props.streamData.chunks
  }


  return result
})

// 提取文档聚合数据 - 支持多种数据结构
const docAggs = computed(() => {
  if (!props.streamData) return []



  // 尝试多种可能的文档数据结构
  let result = []
  if (Array.isArray(props.streamData.reference.doc_aggs)) {
    result = props.streamData.reference.doc_aggs
  } else if (props.streamData.reference?.doc_aggs) {
    result = props.streamData.reference.doc_aggs
  } else if (props.streamData.reference?.documents) {
    result = props.streamData.reference.documents
  }


  return result
})

// 处理引用标记的函数
const processReferences = (content) => {
  if (!content || !references.value.length) return content

  // 替换 ##数字$$ 格式的引用标记
  return content.replace(/##(\d+)\$\$/g, (match, index) => {
    const refIndex = parseInt(index)
    const reference = references.value[refIndex]

    if (reference) {
      return `<span class="reference-marker" data-ref-index="${refIndex}">[引用]</span>`
    }
    return match
  })
}



// 全局点击处理器，用于处理v-html中的引用点击
const handleGlobalClick = (event) => {
  // 检查点击的元素是否是引用标记
  if (event.target.classList.contains('reference-marker')) {
    event.preventDefault()
    const refIndex = parseInt(event.target.dataset.refIndex)
    const reference = references.value[refIndex]

    if (reference) {
      currentReference.value = reference
      showReferenceModal.value = true
    }
  }
}



// 格式化引用内容
const formatReferenceContent = (content) => {
  if (!content) return ''

  // 检查是否包含HTML标签
  const htmlTagRegex = /<\/?[a-z][\s\S]*>/i

  if (htmlTagRegex.test(content)) {
    // 如果包含HTML标签，增强表格样式
    let formattedContent = content

    // 为表格添加样式类
    formattedContent = formattedContent.replace(/<table[^>]*>/gi, '<table class="reference-table">')

    // 为表头添加样式类
    formattedContent = formattedContent.replace(/<th[^>]*>/gi, '<th class="reference-th">')

    // 为表格单元格添加样式类
    formattedContent = formattedContent.replace(/<td[^>]*>/gi, '<td class="reference-td">')

    return formattedContent
  } else {
    // 如果是纯文本，转换换行符为<br>并转义HTML字符
    return content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n/g, '<br>')
  }
}

// 使用v-html渲染，不再需要DOM操作和监听器

</script>

<style lang="scss" scoped>
.hybrid-renderer {
  .think-collapse {
    :deep(.n-collapse-item__header) {
      padding: 12px 16px;
      background-color: rgba(var(--dify_bg_secondary_rgb), 0.5);
      border-radius: 8px 8px 0 0;
      font-size: 13px;
      font-weight: 500;
      color: var(--dify_text_secondary);
    }

    :deep(.n-collapse-item__content-wrapper) {
      border-radius: 0 0 8px 8px;
      overflow: hidden;
    }

    :deep(.n-collapse-item__content-inner) {
      padding: 0;
    }
  }

  .think-content {
    margin: 0;
    padding: 12px 16px;
    background-color: rgba(var(--dify_bg_secondary_rgb), 0.5);
    
    :deep(.markdown-body) {
      color: var(--dify_text_secondary);
      font-size: 13px;
      
      p {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .reply-content {
    :deep(.markdown-body) {
      color: var(--dify_text_primary);
      font-size: 14px;
      line-height: 1.6;

      p {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }

      h1, h2, h3, h4, h5, h6 {
        margin-top: 20px;
        margin-bottom: 12px;
        font-weight: 600;
        color: var(--dify_text_primary);
        
        &:first-child {
          margin-top: 0;
        }
      }

      ul, ol {
        margin-bottom: 12px;
        padding-left: 20px;
      }

      li {
        margin-bottom: 4px;
      }

      code {
        background-color: var(--dify_bg_secondary);
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 0.9em;
      }

      pre {
        background-color: var(--dify_bg_secondary);
        padding: 12px;
        border-radius: 6px;
        overflow-x: auto;
        margin-bottom: 12px;
      }

      blockquote {
        border-left: 3px solid var(--dify_border);
        padding-left: 12px;
        margin: 12px 0;
        color: var(--dify_text_secondary);
      }
    }
  }

  .doc-sources {
    margin-top: 16px;
    padding: 12px 16px;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 8px;
    border-left: 3px solid #007bff;

    .doc-sources-title {
      font-size: 13px;
      font-weight: 600;
      color: #666;
      margin-bottom: 8px;
    }

    .doc-sources-list {
      .doc-item {
        color: #007bff;
        font-weight: 500;
      }

      .doc-separator {
        color: #666;
      }
    }
  }
}

// 引用标记样式
:deep(.reference-marker) {
  display: inline-block;
  background-color: #007bff;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin: 0 2px;
  cursor: pointer;
  vertical-align: super;
  line-height: 1;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #0056b3;
  }
}

// 引用弹窗样式
.reference-popover {
  position: fixed;
  background: white;
  border: 1px solid var(--dify_border, #eeeef0);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  max-height: 400px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  z-index: 9999;
  overflow-y: auto;

  // 箭头指示器
  .popover-arrow {
    position: absolute;
    width: 8px;
    height: 8px;
    background: white;
    border: 1px solid var(--dify_border, #eeeef0);
    transform: rotate(45deg);
  }

  // 不同位置的箭头样式
  &.placement-top .popover-arrow {
    bottom: -5px;
    left: 50%;
    margin-left: -4px;
    border-top: none;
    border-left: none;
  }

  &.placement-bottom .popover-arrow {
    top: -5px;
    left: 50%;
    margin-left: -4px;
    border-bottom: none;
    border-right: none;
  }

  &.placement-left .popover-arrow {
    right: -5px;
    top: 50%;
    margin-top: -4px;
    border-left: none;
    border-bottom: none;
  }

  &.placement-right .popover-arrow {
    left: -5px;
    top: 50%;
    margin-top: -4px;
    border-right: none;
    border-top: none;
  }

  .reference-content {
    color: var(--dify_text_primary, #17181a);
    margin-bottom: 8px;
    white-space: pre-wrap;
    word-break: break-word;

    // HTML表格样式
    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 8px 0;
      font-size: 12px;

      th, td {
        border: 1px solid var(--dify_border, #eeeef0);
        padding: 6px 8px;
        text-align: left;
        vertical-align: top;
      }

      th {
        background-color: var(--dify_bg_secondary, #f7f8fa);
        font-weight: 600;
        color: var(--dify_text_primary, #17181a);
      }

      td {
        background-color: white;
      }

      tr:nth-child(even) td {
        background-color: rgba(var(--dify_bg_secondary_rgb, 247, 248, 250), 0.3);
      }
    }

    // HTML列表样式
    :deep(ul), :deep(ol) {
      margin: 8px 0;
      padding-left: 16px;

      li {
        margin-bottom: 4px;
      }
    }

    // HTML段落样式
    :deep(p) {
      margin: 8px 0;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    // HTML标题样式
    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      margin: 12px 0 8px 0;
      font-weight: 600;
      color: var(--dify_text_primary, #17181a);

      &:first-child {
        margin-top: 0;
      }
    }

    :deep(h1) { font-size: 16px; }
    :deep(h2) { font-size: 15px; }
    :deep(h3) { font-size: 14px; }
    :deep(h4) { font-size: 13px; }
    :deep(h5) { font-size: 12px; }
    :deep(h6) { font-size: 12px; }

    // HTML代码样式
    :deep(code) {
      background-color: var(--dify_bg_secondary, #f7f8fa);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 11px;
    }

    :deep(pre) {
      background-color: var(--dify_bg_secondary, #f7f8fa);
      padding: 8px;
      border-radius: 4px;
      overflow-x: auto;
      margin: 8px 0;

      code {
        background: none;
        padding: 0;
      }
    }

    // HTML强调样式
    :deep(strong), :deep(b) {
      font-weight: 600;
    }

    :deep(em), :deep(i) {
      font-style: italic;
    }

    // HTML链接样式
    :deep(a) {
      color: #007bff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    // HTML引用样式
    :deep(blockquote) {
      border-left: 3px solid var(--dify_border, #eeeef0);
      padding-left: 12px;
      margin: 8px 0;
      color: var(--dify_text_secondary, #7c7d81);
      font-style: italic;
    }


  }


}
</style>

<!-- 全局样式，专门用于v-html渲染的表格内容 -->
<style>
/* 引用模态框中的表格样式 - 不使用scoped确保v-html内容生效 */
.reference-modal-content .reference-table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 12px 0 !important;
  font-size: 13px !important;
  background-color: #ffffff !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.reference-modal-content .reference-th {
  background-color: #f7f8fa !important;
  color: #17181a !important;
  font-weight: 600 !important;
  padding: 12px 8px !important;
  text-align: left !important;
  border: 1px solid #eeeef0 !important;
  border-bottom: 2px solid #eeeef0 !important;
}

.reference-modal-content .reference-td {
  padding: 10px 8px !important;
  border: 1px solid #e5e7eb !important;
  color: #17181a !important;
  vertical-align: top !important;
}

.reference-modal-content .reference-td:hover {
  background-color: #f7f8fa !important;
}

.reference-modal-content .reference-table tr:nth-child(even) {
  background-color: rgba(247, 248, 250, 0.3) !important;
}

.reference-modal-content .reference-table tr:hover {
  background-color: rgba(247, 248, 250, 0.6) !important;
}

/* 确保引用文档名称样式生效 */
.reference-modal-content .reference-doc-name {
  color: #007bff !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  padding-top: 8px !important;
  border-top: 1px solid #eeeef0 !important;
}
</style>
