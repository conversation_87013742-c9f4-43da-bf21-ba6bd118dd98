<script setup>
import { computed, useAttrs, ref, onMounted, watch } from "vue";
import { Icon } from "@iconify/vue";
import { getLocalIcon, getIconAsync, hasLocalIcon } from "@/utils/iconLoader";

const props = defineProps({
  icon: String,
  hoverColor: String,
  hover: <PERSON>ole<PERSON>,
});

const attrs = useAttrs();
const localSvgContent = ref(null);
const useLocalIcon = ref(false);
const isLoading = ref(false);

const bindAttrs = computed(() => {
  return {
    style: attrs.style || "",
    class: attrs.class || "",
  };
});

// 加载图标
const loadIcon = async () => {
  if (!props.icon) {
    localSvgContent.value = null;
    useLocalIcon.value = false;
    isLoading.value = false;
    return;
  }

  // 首先尝试从缓存获取
  const cachedIcon = getLocalIcon(props.icon);
  if (cachedIcon) {
    localSvgContent.value = cachedIcon;
    useLocalIcon.value = true;
    isLoading.value = false;
    return;
  }

  // 如果缓存中没有，检查是否应该有本地图标
  if (hasLocalIcon(props.icon)) {
    isLoading.value = true;

    try {
      const svgContent = await getIconAsync(props.icon);
      if (svgContent) {
        localSvgContent.value = svgContent;
        useLocalIcon.value = true;
      } else {
        useLocalIcon.value = false;
      }
    } catch (error) {
      console.warn(`Failed to load local icon: ${props.icon}`, error);
      useLocalIcon.value = false;
    }

    isLoading.value = false;
  } else {
    // 没有本地图标，使用 iconify
    useLocalIcon.value = false;
    isLoading.value = false;
  }
};

onMounted(() => {
  loadIcon();
});

// 监听 icon 属性变化
watch(() => props.icon, () => {
  loadIcon();
});
</script>

<template>
  <!-- 使用本地 SVG -->
  <div
    v-if="useLocalIcon && localSvgContent && !isLoading"
    class="svg-icon local-svg"
    :class="[hoverColor ? `hover:text-[${hoverColor}]` : '']"
    v-bind="bindAttrs"
    v-html="localSvgContent"
  />
  <!-- 回退到 iconify，但只在确认本地图标不存在时才显示 -->
  <Icon
    v-else-if="!isLoading && !useLocalIcon"
    class="svg-icon iconify-icon"
    :class="[hoverColor ? `hover:text-[${hoverColor}]` : '']"
    :icon="icon || ''"
    v-bind="bindAttrs"
  />
  <!-- 加载状态 -->
  <div
    v-else-if="isLoading"
    class="svg-icon loading"
    :class="[hoverColor ? `hover:text-[${hoverColor}]` : '']"
    v-bind="bindAttrs"
  >
    <!-- 可以添加一个简单的加载占位符 -->
  </div>
</template>
<style lang="scss" scoped>
.svg-icon {
  cursor: pointer;
  border-radius: 5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  // &.has-hover:hover {
  //   color: #3c3c3c;
  // }
  // &:hover {
  //   color: #3c3c3c;
  // }
}

.local-svg {
  :deep(svg) {
    width: 1em;
    height: 1em;
    fill: currentColor;
  }
}

.loading {
  width: 1em;
  height: 1em;
  opacity: 0.5;
}

// .dark {
//   .svg-icon {
//     &.has-hover:hover {
//       color: #000000;
//     }
//   }
// }
</style>
