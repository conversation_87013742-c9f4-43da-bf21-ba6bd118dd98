<script setup>
import { computed, useAttrs } from "vue";
import { Icon } from "@iconify/vue";

const props = defineProps({
  icon: String,
  hoverColor: String,
  hover: Boolean,
});

const attrs = useAttrs();

const bindAttrs = computed(() => {
  return {
    style: attrs.style || "",
    class: attrs.class || "",
  };
});
</script>

<template>
  <Icon class="svg-icon" :class="[hoverColor ? `hover:text-[${hoverColor}]` : '']" :icon="icon || ''" v-bind="bindAttrs" />
</template>
<style lang="scss" scoped>
.svg-icon {
  cursor: pointer;
  border-radius: 5px;

  // &.has-hover:hover {
  //   color: #3c3c3c;
  // }
  // &:hover {
  //   color: #3c3c3c;
  // }
}

// .dark {
//   .svg-icon {
//     &.has-hover:hover {
//       color: #000000;
//     }
//   }
// }
</style>
