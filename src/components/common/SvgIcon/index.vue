<script setup>
import { computed, useAttrs, ref, onMounted, watch } from "vue";
import { Icon } from "@iconify/vue";
import iconMap from "@/assets/icons/icon-map.json";

const props = defineProps({
  icon: String,
  hoverColor: String,
  hover: <PERSON><PERSON><PERSON>,
});

const attrs = useAttrs();
const localSvgContent = ref(null);
const useLocalIcon = ref(false);

const bindAttrs = computed(() => {
  return {
    style: attrs.style || "",
    class: attrs.class || "",
  };
});

// 尝试加载本地 SVG 图标
const loadLocalIcon = async () => {
  if (!props.icon) return;

  // 检查是否有本地图标映射
  if (iconMap[props.icon]) {
    try {
      const [collection, name] = props.icon.split(':');
      if (!collection || !name) return;

      // 尝试从 public 目录获取本地 SVG 文件
      const response = await fetch(`/icons/${collection}/${name}.svg`);
      if (response.ok) {
        localSvgContent.value = await response.text();
        useLocalIcon.value = true;
        return;
      }
    } catch (error) {
      console.warn(`Failed to load local icon: ${props.icon}`, error);
    }
  }

  // 如果本地文件不存在，使用 iconify
  useLocalIcon.value = false;
};

onMounted(() => {
  loadLocalIcon();
});

// 监听 icon 属性变化
watch(() => props.icon, () => {
  loadLocalIcon();
});
</script>

<template>
  <!-- 使用本地 SVG -->
  <div
    v-if="useLocalIcon && localSvgContent"
    class="svg-icon"
    :class="[hoverColor ? `hover:text-[${hoverColor}]` : '']"
    v-bind="bindAttrs"
    v-html="localSvgContent"
  />
  <!-- 回退到 iconify -->
  <Icon
    v-else
    class="svg-icon"
    :class="[hoverColor ? `hover:text-[${hoverColor}]` : '']"
    :icon="icon || ''"
    v-bind="bindAttrs"
  />
</template>
<style lang="scss" scoped>
.svg-icon {
  cursor: pointer;
  border-radius: 5px;

  // &.has-hover:hover {
  //   color: #3c3c3c;
  // }
  // &:hover {
  //   color: #3c3c3c;
  // }
}

// .dark {
//   .svg-icon {
//     &.has-hover:hover {
//       color: #000000;
//     }
//   }
// }
</style>
