<template>
  <div class="flex flex-col h-full overflow-hidden">
    <AppCard v-if="$slots.default" class="mb-[20px] min-h-15 border rounded bg-gray-100	">
      <form class="flex justify-between p-1" @submit.prevent="handleSearch()">
        <n-scrollbar x-scrollable>
          <n-space :wrap="!expand || isExpanded" :size="[32, 16]" class="p-2.5">
            <slot />
          </n-space>
        </n-scrollbar>
        <div class="flex-shrink-0 p-2.5">
          <n-button v-if="showReset" ghost type="primary" @click="handleReset">
            <SvgIcon icon="ri:reset-left-line"></SvgIcon>
            重置
          </n-button>
          <n-button attr-type="submit" class="ml-5" type="primary">
            <SvgIcon icon="lucide:search"></SvgIcon>
            搜索
          </n-button>

          <template v-if="expand">
            <n-button v-if="!isExpanded" type="primary" text @click="toggleExpand">
              <i class="i-fe:chevrons-down ml-1" />
              展开
            </n-button>
            <n-button v-else text type="primary" @click="toggleExpand">
              <i class="i-fe:chevrons-up ml-1" />
              收起
            </n-button>
          </template>
        </div>
      </form>
    </AppCard>
    <!--  v-model:checked-row-keys="checkedRowKeys" -->
    <div class="flex-1 overflow-auto">
      <NDataTable v-model:checked-row-keys="checkedRowKeys" :remote="remote" :loading="loading" :scroll-x="scrollX"
        :columns="columns" :data="tableData" :row-key="(row) => row[rowKey]"
        :pagination="isPagination ? pagination : false" @update:checked-row-keys="onChecked" @update:page="onPageChange"
        :on-update:page-size="onPageSizeChange" size="small" />
    </div>
  </div>
</template>

<script setup>
import { NDataTable } from 'naive-ui'
import { utils, writeFile } from 'xlsx'
import { watch } from 'vue'

const checkedRowKeys = ref([])



const props = defineProps({
  /**
   * @remote true: 后端分页  false： 前端分页
   */
  remote: {
    type: Boolean,
    default: true,
  },
  /**
   * @isPagination 是否分页
   */
  isPagination: {
    type: Boolean,
    default: true,
  },
  scrollX: {
    type: Number,
    default: 1200,
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  columns: {
    type: Array,
    required: true,
  },
  /** queryBar中的参数 */
  queryItems: {
    type: Object,
    default() {
      return {}
    },
  },
  /**
   * ! 约定接口入参出参
   * 分页模式需约定分页接口入参
   *    @pageSize 分页参数：一页展示多少条，默认10
   *    @pageNo   分页参数：页码，默认1
   * 需约定接口出参
   *    @pageData 分页模式必须,非分页模式如果没有pageData则取上一层data
   *    @total    分页模式必须，非分页模式如果没有total则取上一层data.length
   */
  getData: {
    type: Function,
    required: true,
  },
  /** 是否支持展开 */
  expand: Boolean,
  showReset: {
    type: Boolean,
    default: true,
  },
  checkedKeys: {
    type: Array,
    required: true,
  }
})

watch(() => props.checkedKeys, (val) => {
  checkedRowKeys.value = val
  console.log(val);

}, {
  deep: true,
  immediate: true
})

const emit = defineEmits(['update:queryItems', 'onChecked', 'onDataChange', 'post-processing'])
const loading = ref(false)
const initQuery = { ...props.queryItems }
const tableData = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40, 50],
  showQuickJumper: true,
  showSizePicker: true,
  prefix({ itemCount }) {
    return `共 ${itemCount} 条数据`
  },
})

// 是否展开
const isExpanded = ref(false)

function toggleExpand() {
  isExpanded.value = !isExpanded.value
}

async function handleQuery() {
  try {
    loading.value = true
    let paginationParams = {}
    // 如果非分页模式或者使用前端分页,则无需传分页参数
    if (props.isPagination && props.remote) {
      paginationParams = { page: pagination.page, size: pagination.pageSize }
    }
    const { data } = await props.getData({
      ...props.queryItems,
      ...paginationParams,
    })
    const list = data?.rows || data
    // 列表数据后处理
    emit('post-processing', list)
    tableData.value = list
    pagination.itemCount = data.total ?? data.length
    if (pagination.itemCount && !tableData.value.length && pagination.page > 1) {
      // 如果当前页数据为空，且总条数不为0，则返回上一页数据
      onPageChange(pagination.page - 1)
    }
  }
  catch (error) {
    console.error(error)
    tableData.value = []
    pagination.itemCount = 0
  }
  finally {
    emit('onDataChange', tableData.value)
    loading.value = false
  }
}

function handleSearch(keepCurrentPage = false) {
  if (keepCurrentPage || !props.remote) {
    handleQuery()
  }
  else {
    onPageChange(1)
  }
}
async function handleReset() {
  const queryItems = { ...props.queryItems }
  for (const key in queryItems) {
    queryItems[key] = null
  }
  emit('update:queryItems', { ...queryItems, ...initQuery })
  await nextTick()
  pagination.page = 1
  handleQuery()
}
function onPageChange(currentPage) {
  pagination.page = currentPage
  if (props.remote) {
    handleQuery()
  }
}
function onPageSizeChange(currentPageSize) {
  pagination.pageSize = currentPageSize
  if (props.remote) {
    handleQuery()
  }
}
function onChecked(rowKeys) {
  if (props.columns.some(item => item.type === 'selection')) {
    emit('onChecked', rowKeys)
  }
}
function handleExport(columns = props.columns, data = tableData.value) {
  if (!data?.length)
    return $message.warning('没有数据')
  const columnsData = columns.filter(item => !!item.title && !item.hideInExcel)
  const thKeys = columnsData.map(item => item.key)
  const thData = columnsData.map(item => item.title)
  const trData = data.map(item => thKeys.map(key => item[key]))
  const sheet = utils.aoa_to_sheet([thData, ...trData])
  const workBook = utils.book_new()
  utils.book_append_sheet(workBook, sheet, '数据报表')
  writeFile(workBook, '数据报表.xlsx')
}

defineExpose({
  handleSearch,
  handleReset,
  handleExport,
})
</script>
