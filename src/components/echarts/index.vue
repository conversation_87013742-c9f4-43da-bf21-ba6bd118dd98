<template>
    <div ref="chart" style="width: 100%; height: 100%;"></div>
</template>
  <script setup>
import { onMounted, watch } from 'vue'
const props = defineProps({
    option: Object,
})
  
const chart = ref(null);
const instance = getCurrentInstance(); // 获取当前组件实例

watch(() => props.option, (newVal) => {
    echartsInit()
}, {deep: true})


onMounted(() => {
    echartsInit()
})

const echartsInit = () => {
    // 通过 instance.proxy 访问全局属性
    const myChart = instance.proxy.$echarts.init(chart.value);
    myChart.setOption(props.option);
}
</script>

<style lang="scss" scoped>
</style>