<template>
  <div class="disposal-card">
    <div class="card-header">
      <SvgIcon icon="material-symbols:security" class="header-icon" />
      <span class="header-title">安全处置任务</span>
    </div>
    
    <div class="card-content">
      <div class="summary-section">
        <h4>任务摘要</h4>
        <p class="summary-text">{{ summary }}</p>
      </div>
      
      <div class="action-section">
        <n-button 
          type="primary" 
          size="large"
          :loading="executing"
          :disabled="executed"
          @click="handleExecute"
          class="execute-btn"
        >
          <template #icon>
            <SvgIcon :icon="executed ? 'material-symbols:check-circle' : 'material-symbols:play-arrow'" />
          </template>
          {{ executed ? '已执行' : executing ? '执行中...' : '确认执行' }}
        </n-button>
      </div>
      
      <div v-if="executing" class="progress-section">
        <n-progress 
          type="line" 
          :percentage="progress" 
          :show-indicator="false"
          status="info"
        />
        <p class="progress-text">正在执行安全处置任务，预计需要15秒...</p>
      </div>
      
      <div v-if="executed && result" class="result-section">
        <h4>执行结果</h4>
        <div class="result-content">
          {{ result }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { NButton, NProgress } from 'naive-ui'
import SvgIcon from '@/components/common/SvgIcon/index.vue'

const props = defineProps({
  originalMessage: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['execute-complete'])

const executing = ref(false)
const executed = ref(false)
const progress = ref(0)
const result = ref('')
const taskId = ref('')

// 从原始消息中提取摘要
const summary = computed(() => {
  const message = props.originalMessage.replace(/^处置：/, '').trim()

  // 根据不同场景生成摘要
  if (message.includes('4A系统') && message.includes('欧拉操作系统')) {
    return '检测到系统升级改造需求，将执行网络安全三同步检查，包括主机安全状态验证、数据库连接测试、网络通信检查等操作。'
  } else if (message.includes('勒索病毒')) {
    return '检测到勒索病毒告警，将立即执行病毒隔离、系统扫描、安全加固等应急响应措施。'
  } else if (message.includes('漏洞')) {
    return '检测到安全漏洞，将执行漏洞扫描、风险评估、补丁部署等安全加固措施。'
  } else if (message.includes('入侵')) {
    return '检测到入侵行为，将立即执行入侵检测、威胁分析、安全隔离等应急响应。'
  } else {
    return `安全处置任务：${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`
  }
})

// Mock执行接口
const mockExecuteApi = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true, taskId: 'TASK_' + Date.now() })
    }, 1000)
  })
}

// Mock查询结果接口
const mockQueryResult = (taskId) => {
  return new Promise((resolve) => {
    const message = props.originalMessage
    let mockResult = ''
    
    if (message.includes('4A系统') && message.includes('欧拉操作系统')) {
      mockResult = `网络安全三同步检查完成：
✅ 主机安全状态：正常 (检查了12台欧拉操作系统主机)
✅ 数据库连接：正常 (验证了8个数据库实例)
✅ 网络通信：正常 (测试了所有关键网络链路)
⚠️  发现3个需要关注的安全配置项，已通过工单系统推送
📋 工单编号：WO-${Date.now()}
🕐 预计凌晨4点完成割接，系统将持续监控`
    } else if (message.includes('勒索病毒')) {
      mockResult = `勒索病毒应急处置完成：
🔒 病毒隔离：已隔离受感染主机 (IP: *************-105)
🛡️  系统扫描：全网扫描完成，发现并清除6个威胁文件
🔧 安全加固：已更新防病毒规则，加强端点防护
📊 影响评估：影响范围已控制，业务系统正常运行
📋 详细报告已生成，工单编号：SEC-${Date.now()}`
    } else {
      mockResult = `安全处置任务执行完成：
✅ 任务状态：成功
📋 工单编号：TASK-${Date.now()}
🕐 执行时间：${new Date().toLocaleString()}
📝 详细信息请查看工单系统`
    }
    
    resolve({ success: true, result: mockResult })
  })
}

// 处理执行按钮点击
const handleExecute = async () => {
  if (executing.value || executed.value) return
  
  executing.value = true
  progress.value = 0
  
  try {
    // 1. 调用执行接口
    const executeResult = await mockExecuteApi()
    console.log('执行接口调用成功:', executeResult)
    
    // 2. 开始轮询查询结果
    taskId.value = executeResult.taskId
    let pollCount = 0
    const maxPolls = 15 // 15秒，每秒轮询一次
    
    const pollInterval = setInterval(async () => {
      pollCount++
      progress.value = Math.min((pollCount / maxPolls) * 100, 95)
      
      try {
        const queryResult = await mockQueryResult(taskId.value)
        
        if (queryResult.success || pollCount >= maxPolls) {
          clearInterval(pollInterval)
          progress.value = 100
          
          setTimeout(() => {
            executing.value = false
            executed.value = true
            result.value = queryResult.result
            
            // 通知父组件执行完成
            emit('execute-complete', {
              success: true,
              result: queryResult.result,
              originalMessage: props.originalMessage
            })
          }, 500)
        }
      } catch (error) {
        console.error('查询结果失败:', error)
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval)
          executing.value = false
          result.value = '执行超时，请稍后查看工单系统获取结果'
          
          emit('execute-complete', {
            success: false,
            result: '执行超时，请稍后查看工单系统获取结果',
            originalMessage: props.originalMessage
          })
        }
      }
    }, 1000)
    
  } catch (error) {
    console.error('执行失败:', error)
    executing.value = false
    result.value = '执行失败，请重试或联系管理员'
    
    emit('execute-complete', {
      success: false,
      result: '执行失败，请重试或联系管理员',
      originalMessage: props.originalMessage
    })
  }
}
</script>

<style lang="scss" scoped>
.disposal-card {
  max-width: 500px;
  border: 1px solid #e0e0e6;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .header-icon {
    font-size: 20px;
    margin-right: 8px;
  }
  
  .header-title {
    font-size: 16px;
    font-weight: 600;
  }
}

.card-content {
  padding: 20px;
}

.summary-section {
  margin-bottom: 20px;
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
  
  .summary-text {
    margin: 0;
    font-size: 13px;
    line-height: 1.5;
    color: #666;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
  }
}

.action-section {
  margin-bottom: 20px;
  text-align: center;
  
  .execute-btn {
    width: 100%;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
  }
}

.progress-section {
  margin-bottom: 20px;
  
  .progress-text {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #666;
    text-align: center;
  }
}

.result-section {
  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
  
  .result-content {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    padding: 12px;
    font-size: 13px;
    line-height: 1.6;
    white-space: pre-line;
    color: #0369a1;
  }
}
</style>
