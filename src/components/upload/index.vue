<template>
  <n-upload class="mx-auto w-full text-center" :show-file-list="false"
    accept=".png,.jpg,.jpeg,.doc,.docx,.xml,.zip,.pdf" @before-upload="onBeforeUpload"
    :default-file-list="defaultFileList">
    <n-upload-dragger>
      <div class="flex flex-col justify-center items-center">
        <SvgIcon icon="ic:round-upload" :width="40" :height="40"></SvgIcon>
        <n-text class="text-sm color-gray">
          点击区域来上传
        </n-text>
      </div>
    </n-upload-dragger>
  </n-upload>
</template>

<script setup>
import { useClipboard } from '@vueuse/core'
import { lStorage, throttle } from '@/utils'
import { computed, watch } from 'vue'
import Api from './api'

defineOptions({ name: 'FileUpload' })

const props = defineProps({
  maxSize: {
    type: Number,
    default: 1 // MB
  },
  defaultFileList: {
    type: Array,
    default: () => []
  }
});

const { copy, copied } = useClipboard()
let uuid = computed(() => lStorage.get('logininfo').uuid)

const emit = defineEmits(['on-success', 'on-error'])
watch(copied, (val) => {
  if (val)
    $message.success('已复制到剪切板')
})

let fList = ref([])
function onBeforeUpload({ file }) {
  if (!file || !file.type) {
    $message.error('请选择文件')
  }
  if (file.file.size > props.maxSize * 1024 * 1024 * 10) {
    $message.error(`文件大小不能超过${props.maxSize}MB`)
    return false
  }
  $message.success('上传中...')
  const formData = new FormData()
  formData.append('bucket', 'demand-file')
  // formData.append('files', [file])
  formData.append('file', file.file)
  Api.uploadFile(formData).then(({ data }) => {
    $message.success('上传成功')
    emit('on-success', data)
  }).catch((err) => {
    $message.error('上传失败')
    console.log(err)
  }).finally(() => {
    // $message.remove()
  })
  return true
}

async function handleUpload({ file, onFinish }) {
}
</script>
