<template>

  <Transition name="loading-transition" :visible="appStore.showLoading">
    <div v-if="appStore.showLoading" class="loading-full-container">
      <div class="loading-content">
        <div class="spinner-container">
          <div class="spinner-outer-ring"></div>
          <div class="spinner-middle-ring"></div>
          <div class="spinner-inner-ring"></div>
          <div class="spinner-core"></div>
          <div class="particles">
            <span v-for="n in 8" :key="n" class="particle" :style="{ '--i': n }"></span>
          </div>
        </div>
        <div class="loading-text">
          <span>{{ appStore.loadingText }}</span>
          <span class="dots">
            <span class="dot">.</span>
            <span class="dot">.</span>
            <span class="dot">.</span>
          </span>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, watch } from 'vue'
import { useAppStore } from "@/store/modules/app";


const appStore = useAppStore()

const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  text: {
    type: String,
    default: ''
  },
  autoHide: {
    type: Boolean,
    default: false
  },
  duration: {
    type: Number,
    default: 3000
  }
})

const emit = defineEmits(['update:visible', 'after-enter', 'after-leave'])

const localVisible = ref(props.visible)

// Watch for changes in the visible prop
watch(() => props.visible, (newVal) => {
  localVisible.value = newVal
})

// Auto-hide functionality
let hideTimer = null

onMounted(() => {
  if (props.autoHide && props.visible) {
    startHideTimer()
  }
})

onBeforeUnmount(() => {
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
})

function startHideTimer() {
  if (hideTimer) {
    clearTimeout(hideTimer)
  }

  hideTimer = setTimeout(() => {
    localVisible.value = false
    emit('update:visible', false)
  }, props.duration)
}

function onAfterEnter() {
  emit('after-enter')
  if (props.autoHide) {
    startHideTimer()
  }
}

function onAfterLeave() {
  emit('after-leave')
}
</script>

<style lang="scss" scoped>
.loading-full-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(18, 18, 35, 0.92), rgba(28, 28, 45, 0.95));
  backdrop-filter: blur(8px);
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .spinner-container {
    position: relative;
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    perspective: 500px;

    .spinner-outer-ring {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 4px solid transparent;
      border-top-color: #4F46E5;
      border-bottom-color: #10B981;
      border-radius: 50%;
      animation: spin3D 2s linear infinite;
      box-shadow: 0 0 15px rgba(79, 70, 229, 0.5);
    }

    .spinner-middle-ring {
      position: absolute;
      width: 80%;
      height: 80%;
      border: 4px solid transparent;
      border-left-color: #EC4899;
      border-right-color: #F59E0B;
      border-radius: 50%;
      animation: spin3D 1.8s linear infinite reverse;
      box-shadow: 0 0 15px rgba(236, 72, 153, 0.5);
    }

    .spinner-inner-ring {
      position: absolute;
      width: 60%;
      height: 60%;
      border: 4px solid transparent;
      border-top-color: #3B82F6;
      border-bottom-color: #8B5CF6;
      border-radius: 50%;
      animation: spin3D 1.5s linear infinite;
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
    }

    .spinner-core {
      position: absolute;
      width: 30%;
      height: 30%;
      background: radial-gradient(circle, #FFFFFF, #6EE7B7);
      border-radius: 50%;
      animation: pulse3D 1.5s ease-in-out infinite alternate;
      box-shadow: 0 0 20px rgba(110, 231, 183, 0.8);
    }

    .particles {
      position: absolute;
      width: 100%;
      height: 100%;
      transform-style: preserve-3d;
      animation: rotateParticles 8s linear infinite;

      .particle {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 8px;
        height: 8px;
        background-color: #F472B6;
        border-radius: 50%;
        transform: translate(-50%, -50%) rotate(calc(var(--i) * 45deg)) translateX(40px);
        animation: particlePulse 2s ease-in-out infinite;
        animation-delay: calc(var(--i) * 0.1s);
        box-shadow: 0 0 10px #F472B6, 0 0 20px #F472B6;

        &:nth-child(odd) {
          background-color: #38BDF8;
          box-shadow: 0 0 10px #38BDF8, 0 0 20px #38BDF8;
        }

        &:nth-child(3n) {
          background-color: #A78BFA;
          box-shadow: 0 0 10px #A78BFA, 0 0 20px #A78BFA;
        }

        &:nth-child(3n+1) {
          background-color: #34D399;
          box-shadow: 0 0 10px #34D399, 0 0 20px #34D399;
        }
      }
    }
  }

  .loading-text {
    font-size: 22px;
    font-weight: 600;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    letter-spacing: 1px;

    .dots {
      display: inline-flex;
      margin-left: 4px;

      .dot {
        animation: colorDotFade 2s infinite;
        opacity: 0;
        color: #F472B6;
        font-size: 24px;
        transform: translateY(0);
        display: inline-block;

        &:nth-child(1) {
          animation-delay: 0s;
          color: #4F46E5;
        }

        &:nth-child(2) {
          animation-delay: 0.3s;
          color: #EC4899;
        }

        &:nth-child(3) {
          animation-delay: 0.6s;
          color: #10B981;
        }
      }
    }
  }
}

@keyframes spin3D {
  0% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }

  100% {
    transform: rotateX(360deg) rotateY(180deg) rotateZ(360deg);
  }
}

@keyframes pulse3D {

  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes rotateParticles {
  0% {
    transform: rotateX(0deg) rotateY(0deg);
  }

  100% {
    transform: rotateX(360deg) rotateY(360deg);
  }
}

@keyframes particlePulse {

  0%,
  100% {
    transform: translate(-50%, -50%) rotate(calc(var(--i) * 45deg)) translateX(40px) scale(1);
    opacity: 0.8;
  }

  50% {
    transform: translate(-50%, -50%) rotate(calc(var(--i) * 45deg)) translateX(50px) scale(1.5);
    opacity: 1;
  }
}

@keyframes colorDotFade {

  0%,
  80%,
  100% {
    opacity: 0;
    transform: translateY(0);
  }

  40% {
    opacity: 1;
    transform: translateY(-4px);
  }
}

// Entrance and exit animations
.loading-transition-enter-active {
  animation: scaleIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.loading-transition-leave-active {
  animation: scaleOut 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(1.2);
    filter: blur(10px);
  }

  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
}

@keyframes scaleOut {
  0% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }

  100% {
    opacity: 0;
    transform: scale(0.8);
    filter: blur(10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
</style>
