<!-- agent 弹窗选择组件 -->
<template>
  <n-modal v-model:show="modalShow" :preset="undefined" size="huge" :bordered="false" style="width: 800px;">
    <n-card @close="close()">
      <template #header>
        <header class="modal-header">
          智能体绑定
        </header>
      </template>
      <div class="modal-body">
        <!-- 分类绑定、分组绑定、智能体绑定 n-tab -->
        <n-tabs type="line" v-model:value="tabIndex" style="height: 100%;">
          <n-tab-pane name="1" tab="分类绑定">
            <!-- {{ checkedKeys }} -->
            <n-tree class="mt-2" block-line cascade checkable :data="groupList" check-strategy="parent"
              children-field="children" label-field="name" key-field="id" :default-checked-keys="checkedKeys"
              :default-expand-all="true" @update:checked-keys="updateCheckedKeys" />
          </n-tab-pane>
          <n-tab-pane name="2" tab="分组绑定">
            <n-select class="mt-2" v-model:value="checkLinkIds" :options="linkList" label-field="name"
              value-field="linkId" placeholder="请选择分组" multiple style="width: 100%;" />
          </n-tab-pane>
          <!-- <n-tab-pane name="3" tab="智能体绑定">
            <AppCard class="mb-[30px] min-h-15 border rounded bg-gray-100	">
              <form class="flex justify-between p-4" @submit.prevent="handleSearch()">
                <n-scrollbar x-scrollable>
                  <n-space :size="[32, 16]" class="p-2.5 flex">
                    <MeQueryItem label="id" :label-width="50">
                      <n-input v-model:value="queryItems.id" type="text" clearable />
                    </MeQueryItem>
                    <MeQueryItem label="名称" :label-width="50">
                      <n-input v-model:value="queryItems.name" type="text" clearable >
                        <template #password-invisible-icon></template>
                      </n-input>
                    </MeQueryItem>
                  </n-space>
                </n-scrollbar>
                <div class="flex-shrink-0 p-2.5">
                  <n-button ghost type="primary" @click="handleReset">
                    <SvgIcon icon="ri:reset-left-line"></SvgIcon>
                    重置
                  </n-button>
                  <n-button attr-type="submit" class="ml-5" type="primary">
                    <SvgIcon icon="lucide:search"></SvgIcon>
                    搜索
                  </n-button>
                </div>
              </form>
            </AppCard>
            <n-data-table v-model:checked-row-keys="checkedAgentIds" :row-key="rowKey" :columns="columns"
              :data="agentList" :bordered="false" />
          </n-tab-pane> -->
        </n-tabs>
      </div>
      <template #footer>
        <footer class="modal-footer w-full flex justify-end gap-2">
          <n-button @click="close()">取消</n-button>
          <n-button type="primary" @click="submit()">{{ btnText }}</n-button>
        </footer>
      </template>
    </n-card>
  </n-modal>
</template>
<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { NAvatar, NButton, NSwitch, NTag } from 'naive-ui'
import api from './api'
import $api from '@/api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"

const emit = defineEmits(['submit'])

onMounted(() => {
  initGroup()
})

const tabIndex = ref('1')
const handleTabChange = () => {
  console.log(tabIndex.value);

}

const btnText = computed(() => {
  if (tabIndex.value === '1') {
    return '分类绑定'
  } else if (tabIndex.value === '2') {
    return '分组绑定'
  } else if (tabIndex.value === '3') {
    return '智能体绑定'
  }
})

const linkList = ref([])
const checkLinkIds = ref('')
const initLinkList = async () => {
  const res = await api.linkList()
  linkList.value = res.data
}

const groupList = ref([])
const initGroup = async () => {
  const res = await api.groupList()
  groupList.value = res.data
  console.log(res.data)
}

const agentList = ref([])
const checkedAgentIds = ref([])
const total = ref(0)
const initAgent = async () => {
  const { data } = await $api.getAllAgents()
  agentList.value = data.rows
  total.value = data.total
}

// 分类
const checkedKeys = ref([])
const updateCheckedKeys = (keys) => {
  checkedKeys.value = keys
}
// 分组


// 智能体
const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})
const {
  handleEdit,
  handleDelete,
} = useCrud({
  refresh: () => $table.value?.handleSearch(),
})

const rowKey = (row) => row.id
const columns = [
  {
    type: 'selection',
  },
  { title: 'id', key: 'id', ellipsis: { tooltip: true } },
  { title: '名称', key: 'name', ellipsis: { tooltip: true } },
  { title: '类型', key: 'mode', ellipsis: { tooltip: true } },
  { title: 'icon', key: 'icon', ellipsis: { tooltip: true } },
  { title: '创建时间', key: 'createdAt', ellipsis: { tooltip: true } },
  { title: '描述', key: 'description', ellipsis: { tooltip: true } },
  // {
  //   title: '操作',
  //   key: 'actions',
  //   align: 'right',
  //   fixed: 'right',
  //   hideInExcel: true,
  //   render(row) {
  //     return [
  //       h(
  //         NButton,
  //         {
  //           size: 'small',
  //           style: 'margin-left: 12px;',
  //           onClick: () => handleEdit(row),
  //         },
  //         // TODO  http://************:8088/chat/3u0UH4pTFbEfUxqh
  //         {
  //           default: () => '体验',
  //           icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
  //         },
  //       ),

  //     ]
  //   },
  // },
]




const modalShow = ref(false)
const close = () => {
  modalShow.value = false
}

const loading = ref(false)
const show = async (id) => {
  loading.value = true
  const res = await api.getCompanyById(id).finally(() => {
    loading.value = false
  })
  // 还原选中
  checkedKeys.value = res.data.groupIds
  checkLinkIds.value = res.data.linkIds
  checkedAgentIds.value = res.data.agentIds

  modalShow.value = true
  setTimeout(() => {
    $table.value?.handleSearch()
  }, 100)
}

// const checkedIds = ref([])
// const handleChecked = (data) => {
//   checkedIds.value = data
// }
const submit = () => {
  emit('submit', {
    tabIndex: tabIndex.value,
    checkedKeys: checkedKeys.value,
    checkLinkIds: checkLinkIds.value,
    checkAgentIds: checkedAgentIds.value,
    // checkedIds: $table.value?.getCheckedData()
  })
}
defineExpose({
  show,
  close,
})

watch(() => tabIndex.value, (val) => {
  if (val === '1') {
    initGroup()
  } else if (val === '2') {
    initLinkList()
  } else if (val === '3') {
    initAgent()
  }
})
</script>
<style scoped></style>