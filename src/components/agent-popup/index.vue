<!-- agent 弹窗选择组件 -->
<template>
  <n-modal v-model:show="modalShow" :preset="undefined" size="huge" :bordered="false"
    style="width: 80%;max-height: 80vh;" class="overflow-hidden">
    <n-card @close="close()">
      <template #header>
        <header class="modal-header">
          智能体选择
        </header>
      </template>
      <div class="modal-body flex flex-col overflow-hidden">
        <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" class="overflow-auto" :columns="columns"
          :get-data="$api.getAllAgents" :checkedKeys="checkedKeys" @onChecked="handleChecked" :pa>
          <MeQueryItem label="id" :label-width="50">
            <n-input v-model:value="queryItems.id" type="text" placeholder="请输入id" clearable>
              <template #password-visible-icon></template>
            </n-input>
          </MeQueryItem>
          <MeQueryItem label="名称" :label-width="50">
            <n-input v-model:value="queryItems.name" type="text" placeholder="请输入名称" clearable />
          </MeQueryItem>
        </MeCrud>
      </div>
      <template #footer>
        <footer class="modal-footer w-full flex justify-end gap-2">
          <n-button @click="close()">取消</n-button>
          <n-button type="primary" @click="submit()">确定</n-button>
        </footer>
      </template>
    </n-card>
  </n-modal>
</template>
<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NAvatar, NButton, NSwitch, NTag } from 'naive-ui'
import $api from '@/api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"

const emit = defineEmits(['submit'])


const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

onMounted(() => {
  // $table.value?.handleSearch()
})

const {
  handleEdit,
  handleDelete,
} = useCrud({
  // name: '智能体',
  refresh: () => $table.value?.handleSearch(),
})


const columns = [
  {
    type: 'selection',
  },
  {
    title: '图标', key: 'icon', width: 30, ellipsis: { tooltip: true },
    render(row) {
      return h(
        NAvatar,
        {
          size: 'small',
          round: true,
          src: row.icon,
        },
        {
          fallback: () => h('div', { class: "flex justify-center items-center justify-center w-full h-full text-xs text-[#ffffff] rounded-full bg-[#749BFF]" }, row.name.slice(0, 2)),
        }
      )
    }
  },
  { title: '名称', key: 'name', width: 150, ellipsis: { tooltip: true } },
  // { title: '类型', key: 'mode', width: 150, ellipsis: { tooltip: true } },
  // { title: 'icon', key: 'icon', width: 150, ellipsis: { tooltip: true } },
  { title: '创建时间', key: 'createAt', width: 150, ellipsis: { tooltip: true } },
  { title: '描述', key: 'description', width: 150, ellipsis: { tooltip: true } },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handlePreview(row),
          },
          {
            default: () => '体验',
            icon: () => h(SvgIcon, { icon: 'icon-park-outline:preview-open' }),
          },
        ),

      ]
    },
  },
]

const modalShow = ref(false)
const close = () => {
  modalShow.value = false
}

// 
const checkedKeys = ref([])
const checkedIds = ref([])
const show = (ids = []) => {
  modalShow.value = true
  checkedIds.value = checkedKeys.value = ids
  setTimeout(() => {
    $table.value?.handleSearch()
  }, 100)
}

const handleChecked = (data) => {
  checkedIds.value = data
}
const handlePreview = (row) => {

  window.open(row.url)
}
const submit = () => {
  emit('submit', checkedIds.value)
}
defineExpose({
  show,
  close,
})
</script>
<style lang="scss" scoped>
.modal-body {
  max-height: calc(80vh - 140px);
}
</style>