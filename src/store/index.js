// import { createPinia } from "pinia"
// import { useAppStore } from "./appStore"
// const store = createPinia()

// export default store

// export { useAppStore }

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

export function setupStore(app) {
  const pinia = createPinia()
  pinia.use(piniaPluginPersistedstate)
  app.use(pinia)
}

export * from './modules'
