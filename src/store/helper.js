import api from '@/api'
import { useAppStore } from '@/store/modules/app'

export async function getUserInfo() {
  const res = await api.getUser()
  const { id, name, profile, roles, currentRole } = res.data || {}
  return res.data
}
export async function getUserById() {
  const res = await api.getUserById()
  const appStore = useAppStore()
  res.data.companyId && api.getCompanyById(res.data.companyId).then(rsp => {
    appStore.setPreviewCompanyLogo(rsp?.data?.icon)
  })
  return res.data
}

export async function getPermissions() {
  let asyncPermissions = []
  try {
    const res = await api.getRolePermissions()
    // const res = await api.getMenuList()
    asyncPermissions = res?.data || []
  }
  catch (error) {
    console.error(error)
  }
  return asyncPermissions
}
export async function getLinkPermissions() {
  let asyncLinkPermissions = []
  try {
    const res = await api.linkList()
    // const res = await api.getMenuList()
    asyncLinkPermissions = res?.data || []
  }
  catch (error) {
    console.error(error)
  }
  return asyncLinkPermissions
}
