import { usePermissionStore, useRouterStore, useTabStore, useUserStore } from '@/store'
import { defineStore } from 'pinia'
import api from '@/api'
import { treeTraverse } from "@/utils/tools";
import { useAppStore } from './app'


export const useAgentStore = defineStore('agent', {
  state: () => ({
    currentAgent: null,
    queryParams: {
      page: 1,
      limit: 10,
      name: '',
      status: '',
      sort: 'id',
      order: 'desc',
    },
    groupList: [],
    linkList: [],
    agentList: [],
    footerMenus: [
      {
        name: '我的收藏',
        key: 'favorites',
        iconName: 'lets-icons:favorites-duotone'
      }
    ],
    collectionAgentIds: [],
    collectionAgent: []
  }),
  actions: {
    async initPreview(companyId) {
      const appStore = useAppStore()
      api.getCompanyById(companyId).then(res => {
        appStore.setPreviewCompanyLogo(res?.data?.icon)
      })
      const { data } = await api.getAgentPreview(companyId)
      let { groups, links } = data
      groups = groups.sort((a, b) => a.orderNum - b.orderNum)
      this.initGroupList(groups)
      this.initLinkList(links)
    },
    setCurrentAgent(data) {
      this.currentAgent = data
    },
    async initLinkList(list) {
      // link 中的 agentIds 需要单独查询
      let agentIds = list.map(link => link.agentIds).flat()
      // agentIds = ['2cdb1ad1-fa66-4576-bde5-01ad3341ba0f', '48c27f4e-87dd-4a7a-ae9c-fe00a324fccd', '7bcad2cd-9964-4372-a7f4-356b84cf4e7d']
      const { data } = await api.getAllAgents({ agentIds })
      let { rows } = data

      list.forEach((item, index) => {
        let agents = rows.filter(row => item.agentIds.includes(row.id))
        agents.forEach((agent) => {
          agent.icon = ''
          agent.linkId = item.linkId
        })
        item.agents = agents
        if (agents.length == 1) {
          item.name = agents[0].name
          item.url = agents[0].url
        }
      })
      this.linkList = list
    },
    async initGroupList(list) {
      if (!list || list?.length == 0) {

        await this.initCollectionAgentList()
        const { data } = await api.getHallList()
        const { groups, links } = data
        list = groups.sort((a, b) => a.orderNum - b.orderNum)
        this.initLinkList(links)
      }
      list = list.map((item) => {
        return {
          ...item,
          icon: renderName(item),
        }
      })
      this.groupList = list
      if (list[0]) {
        list[0].active = true
        this.initAgentList(list[0]?.agentIds)
      }
    },

    async initCollectionAgentList() {
      const { data } = await api.getCollectionAgents()
      this.collectionAgentIds = data
    },
    async selectAgentMenu({ key, agentIds = [] }) {
      if (key == 'favorites') {
        treeTraverse(this.groupList, (item) => {
          item.active = false;
        });
        await this.initCollectionAgentList()
        this.initAgentList(this.collectionAgentIds)
      } else {
        treeTraverse(this.footerMenus, (item) => {
          item.active = false;
        });
        this.initAgentList(agentIds)
      }
    },
    async initAgentList(agentIds) {
      // this.setCurrentAgent(null)
      if (!agentIds.length) { this.agentList = []; return }

      // agentIds
      const { data } = await api.getAllAgents({ agentIds })
      let { rows } = data
      const sortList = []
      agentIds.forEach((id) => {
        let agent = rows.find(row => row.id == id)
        if (agent) {
          sortList.push(agent)
        }
      })

      sortList.forEach((item) => {
        if (this.collectionAgentIds.includes(item.id))
          item.isStar = true
      })
      this.agentList = sortList
    },

    setQueryParams(data) {
      this.queryParams = data
    },
  }
})

function renderName(item) {
  // 增加 icon 属性
  return () => h('div', { class: 'flex items-center justify-center w-5 h-5 text-xs text-[#ffffff] rounded-full bg-[#749BFF]' }, item.name?.substring(0, 1))
  // return () => h(SvgIcon, { icon:'fluent-mdl2:join-online-meeting' })
}