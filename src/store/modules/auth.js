import { usePermissionStore, useRouterStore, useTabStore, useUserStore } from '@/store'
import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    access_token: '',
    refresh_token: '',
  }),
  actions: {
    setToken({ access_token, refresh_token }) {
      window.localStorage.setItem('access_token', access_token)
      window.localStorage.setItem('console_token', access_token)
      window.localStorage.setItem('refresh_token', refresh_token)
      this.access_token = access_token
      this.refresh_token = refresh_token
    },
    resetToken() {
      // this.$reset()
      window.localStorage.setItem('access_token', '')
      window.localStorage.setItem('console_token', '')
      window.localStorage.setItem('refresh_token', '')
      this.access_token = ''
      this.refresh_token = ''
    },
    toLogin() {
      const { router, route } = useRouterStore()
      router.replace({
        path: '/login',
        query: route.query,
      })
    },
    async switchCurrentRole(data) {
      this.resetLoginState()
      await nextTick()
      this.setToken(data)
    },
    resetLoginState() {
      const { resetUser } = useUserStore()
      const { resetRouter } = useRouterStore()
      const { resetPermission, accessRoutes } = usePermissionStore()
      const { resetTabs } = useTabStore()
      // 重置路由
      resetRouter(accessRoutes)
      // 重置用户
      resetUser()
      // 重置权限
      resetPermission()
      // 重置Tabs
      resetTabs()
      // 重置token
      this.resetToken()
    },
    async logout() {
      this.resetLoginState()
      this.toLogin()
    },
  },
  persist: {
    key: 'vue-naivue-admin_auth',
  },
})
