
// import { defaultLayout, defaultPrimaryColor, naiveThemeOverrides } from '@/settings'
import { generate, getRgbStr } from '@arco-design/color'
import { useDark } from '@vueuse/core'
import { defineStore } from 'pinia'
import emiter from '@/utils/eventEmitter';


export const useAppStore = defineStore('app', {
  state: () => ({
    collapsed: false,
    isDark: false,
    logo: '',
    isPreview: false, // 是否预览模式
    showLoading: false, // 全局loading
    loadingText: '', // loading文字
    previewCompanyLogo: '', // 预览 logo
    previewCompanyId: '', // 预览的公司id 

    // isDark: useDark(),
    // layout: defaultLayout,
    // primaryColor: defaultPrimaryColor,
    // naiveThemeOverrides,
  }),
  getters: {
    getIsPreview() {
      return this.previewCompanyId && this.previewCompanyId !== '-1'
    },
  },
  actions: {
    setLoading(data) {
      this.showLoading = data.showLoading
      this.loadingText = data.loadingText || '加载中'
    },
    setPreviewCompanyLogo(logo) {
      this.previewCompanyLogo = logo
    },
    setPreviewCompanyId(id) {
      this.previewCompanyId = id
    },
    setPreview(b) {
      this.isPreview = b
    },
    switchCollapsed() {
      this.collapsed = !this.collapsed
    },
    setCollapsed(b) {
      this.collapsed = b
    },
    toggleDark() {
      this.isDark = !this.isDark
    },
    // setLayout(v) {
    //   this.layout = v
    // },
    // setPrimaryColor(color) {
    //   this.primaryColor = color
    // },
    // setThemeColor(color = this.primaryColor, isDark = this.isDark) {
    //   const colors = generate(color, {
    //     list: true,
    //     dark: isDark,
    //   })
    //   document.body.style.setProperty('--primary-color', getRgbStr(colors[5]))
    //   this.naiveThemeOverrides.common = Object.assign(this.naiveThemeOverrides.common || {}, {
    //     primaryColor: colors[5],
    //     primaryColorHover: colors[4],
    //     primaryColorSuppl: colors[4],
    //     primaryColorPressed: colors[6],
    //   })
    // },
  },
  persist: {
    pick: ['collapsed', 'layout', 'primaryColor', 'naiveThemeOverrides'],
    storage: sessionStorage,
  },
})
