import { isExternal } from '@/utils'
import { hyphenate } from '@vueuse/core'
import { defineStore } from 'pinia'
const routeComponents = import.meta.glob('@/views/**/*.vue')

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    accessRoutes: [],
    permissions: [],
    menus: [],
  }),
  actions: {
    setPermissions(permissions, linkList) {
      permissions = permissions.filter(item => item.visible === '0' && item.status === '0')

      this.permissions = permissions
      this.menus = this.permissions
        // 类型（M目录 C菜单 F按钮
        .filter(item => item.menuType === 'C')
        .map(item => this.getMenuItem(item))
        .filter(item => !!item)
        .sort((a, b) => a.order - b.order)
      // 判断 this.accessRoutes 中是否有根目录的重定向
      // menu 需要单独拼接 link
      // if (linkList?.length > 0) {
      //   linkList = this.generateLink2Menu(linkList)
      //   // 插入位置需要再智能体大厅菜单的后面
      //   const index = this.menus.findIndex(item => item.path === '/agents')
      //   if (index > -1)
      //     this.menus.splice(index + 1, 0, ...linkList)
      //   else
      //     this.menus.push(...linkList)
      //   // this.menus.push(...linkList)
      // }
      const redirect = this.accessRoutes.find(item => item.path === '/')
      if (!redirect)
        this.accessRoutes.push({ path: '/', redirect: this.menus[0].path })

    },
    generateLink2Menu(links) {
      const menus = links.map(link => {
        const { linkId, name, agentIds } = link
        return {
          isLink: true,
          label: name,
          id: linkId,
          children: agentIds.map((agentId, index) => {
            return {
              agentId,
              label: '智能体 ' + index
            }
          })
          // agentIds
          // menuName: link.name,
          // path: link.path,
          // originPath: link.path,
          // icon: link.icon,
          // menuType: link.menuType,
          // order: link.orderNum ?? 0,
          // navigation: link.navigation ?? '1',
        }
      })
      return menus
    },
    getMenuItem(item, parent) {
      // visible 0 展示
      const show = item.visible === '0' ? true : false
      const enable = item.status === '0' ? true : false
      const route = this.generateRoute(item, parent, show ? null : parent?.key)
      // status 0 启用
      if (enable && route.path && !route.path.startsWith('http'))
        this.accessRoutes.push(route)
      if (!show)
        return null
      const menuItem = {
        label: route.meta.title,
        code: item.code,
        menuName: route.meta.title,
        path: route.path,
        originPath: route.meta.originPath,
        icon: item.icon,
        menuType: item.menuType,
        order: item.orderNum ?? 0,
        navigation: item.navigation ?? '1',
      }
      const children = item.children?.filter(item => item.menuType === 'C') || []
      if (children.length) {
        menuItem.children = children
          .map(child => this.getMenuItem(child, menuItem))
          .filter(item => !!item)
          .sort((a, b) => a.order - b.order)
        if (!menuItem.children.length)
          delete menuItem.children
      }
      return menuItem
    },
    generateRoute(item, parent, parentKey) {
      let originPath
      if (isExternal(item.path)) {
        originPath = item.path
        item.component = '/src/views/iframe/index.vue'
        item.path = `/iframe/${hyphenate(item.code)}`
      }
      // 不是/开头的子路由需要机型进行路由拼接
      if (item.path && !item.path.startsWith('/')) {
        item.path = `${parent?.path || ''}/${item.path}`
      }
      // 处理 children
      if (item.children?.length) {
        // 父节点增加 重定向
        const subPath = item.children.sort((a, b) => a.orderNum - b.orderNum)[0]?.path
        item.redirect = subPath.startsWith('/') ? subPath : `${item.path}/${subPath}`

        item.children = item.children
          .map(child => this.generateRoute(child, item, item.code))
          .filter(item => !!item)
          .sort((a, b) => a.order - b.order)
      }
      item.component = routeComponents[item.component] || item.component || undefined
      return {
        code: item.code,
        order: item.orderNum,
        path: item.path,
        visible: item.visible,
        menuName: item.menuName,
        status: item.status,
        menuType: item.menuType,
        redirect: item.redirect,
        component: item.component,
        children: item.children,
        navigation: item.navigation ?? '1',
        icon: item.icon,
        meta: {
          navigation: item.navigation ?? '1',
          originPath,
          // icon: item.icon,
          title: item.menuName,
          layout: item.layout,
          keepAlive: !!item.isCache,
          parentKey,
          btns: item.children
            ?.filter(item => item.type === 'BUTTON')
            .map(item => ({ code: item.code, name: item.menuName })),
        },
      }
    },
    resetPermission() {
      this.$reset()
    },
  },
})
