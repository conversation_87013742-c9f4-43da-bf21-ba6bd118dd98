.common_page {
  // width: 100%;
  height: 100%;
  // display: flex;
  // overflow: auto;
  // .left {
  //   width: 25%;
  //   height: 100%;
  //   margin-right: 8px;
  //   overflow-y: auto;
  // }
  // .right {
  //   flex: 1;
  //   overflow-y: scroll;
  // }
}

.content {
  display: flex;
  flex-direction: column;
  * {
    // white-space: break-spaces;
  }
  pre {
    code {
      white-space: break-spaces;
    }
  }
}
/* 滚动条整体部分 */
::-webkit-scrollbar {
  width: 2px; /* 横向滚动条的宽度 */
  height: 2px; /* 纵向滚动条的高度 */
}

/* 滚动条的滑块部分 */
::-webkit-scrollbar-thumb {
  background-color: #ffffff !important; /* 滑块的背景颜色 */
  // background-color: transparent;
  border-radius: 6px; /* 滑块的圆角 */
}

.msg-item__box {
  a {
    display: block;
    color: #66a1ff;
  }
}

.common_page {
  .n-tabs-nav {
    padding: 5px 0 10px 0;
  }
}

.n-tabs {
  .n-tab-pane {
    padding: 0 !important;
  }
  .n-tabs-tab {
    padding: 5px 0 !important;
  }
}

// 全局央视覆盖
// .n-menu .n-menu-item-content {
//   border-radius: 40px;
// }
// .n-menu .n-menu-item-content.n-menu-item-content--selected:before {
//   border-radius: 40px;
// }
// .n-menu .n-menu-item-content.n-menu-item-content--selected:hover {
//   border-radius: 40px;
// }
// .n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover::before {
//   border-radius: 40px;
// }
// .n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover::before {
//   background-color: #DCE7FF !important;
// }
