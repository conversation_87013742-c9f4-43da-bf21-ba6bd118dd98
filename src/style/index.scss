@import url('./common.scss');
@import url('./theme.scss');
@import url('./dark.scss');
@import url('./hljs/index.css');
@import url('./lib/github-markdown.css');
@import url('./hljs/index.css');
* {
  font-family: PingFang SC, Microsoft YaHei;
}
body {
  padding: 0;
  margin: 0;
  outline: none;
}
html,
body,
#app {
  height: 100%;
}
body {
  margin: 0;
  place-items: center;
  min-width: 320px;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* 滚动条 */
::-webkit-scrollbar {
  width: 2px;
  border-radius: 2px; /* 整体 圆角 */
}
::-webkit-scrollbar-thumb {
  background: #dcdfe6;
}

.prompt {
  border-radius: 10px;
  // background: #0d1116;
  // border: 1px solid transparent;
  transition: all 0.2s ease 0s;
  .textarea__inner {
    color: rgb(255, 255, 255);
    line-height: 24px;
    background: transparent !important;
    padding: 0px !important;
    font-size: inherit !important;
    resize: none !important;
    box-shadow: none !important;
    .n-input-wrapper {
      resize: none !important;
    }
  }
}
.footer {
  .label {
    color: rgb(153, 155, 172);
  }
  .val:hover {
    color: var(--chatfire_color_primary);
  }
}
.prompt-btn__primary {
  background: linear-gradient(
    89.86deg,
    rgb(167, 255, 26),
    rgb(130, 250, 194),
    rgb(71, 212, 255)
  );
}
