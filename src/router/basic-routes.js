export const basicRoutes = [
  // {
  //   path: "/", redirect: '/agents', children: [
  //     { path: "/agents", component: () => import("@/views/agents/index.vue") },
  //     { path: "/report", component: () => import("@/views/report/index.vue") },
  //     // 超级管理员 审核
  //     { path: "/demand/examine", component: () => import("@/views/demand/examine/index.vue") },
  //     // 平台管理员 上传
  //     { path: "/demand/apply", component: () => import("@/views/demand/apply/index.vue") },
  //     { path: "/workflow", component: () => import("@/views/workflow/index.vue") },
  //     {
  //       path: "/admin", component: AdminLayout, redirect: "/admin/organization", children: [
  //         { path: "user", component: () => import("@/views/admin/user/index.vue") },
  //         { path: "department", component: () => import("@/views/admin/department/index.vue") },
  //         { path: "role", component: () => import("@/views/admin/role/index.vue") },
  //         { path: "resource", component: () => import("@/views/admin/resource/index.vue") },
  //         { path: "dictionary", component: () => import("@/views/admin/dictionary/index.vue") },
  //         { path: "organization", component: () => import("@/views/admin/organization/index.vue") },
  //         { path: "agent/category", component: () => import("@/views/admin/agent-category/index.vue") },
  //       ]
  //     },
  //   ]
  // },
  { path: "/login", component: () => import("@/views/login/index.vue") },
  { path: "/sso", component: () => import("@/views/sso/index.vue") },
  { path: "/reportConfig", component: () => import("@/views/reportConfig/index.vue") },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: () => import('@/views/knowledge/index.vue'),
    meta: {
      title: '知识库管理'
    }
  },
  {
    path: '/knowledge/documents/:id',
    name: 'KnowledgeDocuments',
    component: () => import('@/views/knowledge/documents/index.vue'),
    meta: {
      title: '文档管理'
    }
  },
  {
    path: '/knowledge/chat/:id',
    name: 'KnowledgeChat',
    component: () => import('@/views/knowledge/chat/index.vue'),
    meta: {
      title: '知识库问答'
    }
  },
  {
    path: '/icon-test',
    name: 'IconTest',
    component: () => import('@/views/icon-test.vue'),
    meta: {
      title: '图标测试'
    }
  }
]
