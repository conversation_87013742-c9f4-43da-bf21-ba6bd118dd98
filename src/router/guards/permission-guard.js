import api from '@/api'
import { useAuthStore, usePermissionStore, useUserStore } from '@/store'
import { getPermissions, getLinkPermissions, getUserInfo, getUserById } from '@/store/helper'

const WHITE_LIST = ['/login', '/404', '/sso']
export function createPermissionGuard(router) {
  router.beforeEach(async (to) => {
    const authStore = useAuthStore()
    const token = authStore.access_token

    /** 没有token */
    // if (!token) {
    //   if (WHITE_LIST.includes(to.path)) return true
    //   return { path: 'login', query: { ...to.query, redirect: to.path } }
    // }


    if (WHITE_LIST.includes(to.path))
      return true

    const userStore = useUserStore()
    const permissionStore = usePermissionStore()
    let routes = []
    if (!userStore.userInfo) {
      // 获取用户信息，获取权限，获取 AI 分组信息
      const [user, permissions, linkList] = await Promise.all([getUserById(), getPermissions(), getLinkPermissions()])
      userStore.setUser(user)
      permissionStore.setPermissions(permissions, linkList)
      const routeComponents = import.meta.glob('@/views/**/*.vue')
      permissionStore.accessRoutes.forEach((route) => {
        route.component = routeComponents[route.component] || route.component || undefined
        !router.hasRoute(route.name) && router.addRoute(route)
      })
      return { ...to, replace: true }
    } else {
      routes = router.getRoutes()
      // 有token的情况
      if (to.path === '/')
        // 跳转第一个路由
        return { path: routes[0].path, replace: true }
    }


    if (routes.find(route => route.name === to.name))
      return true

    // 判断是无权限还是404
    const { data: hasMenu } = await api.validateMenuPath(to.path)
    return hasMenu
      ? { name: '403', query: { path: to.fullPath }, state: { from: 'permission-guard' } }
      : { name: '404', query: { path: to.fullPath } }
  })
}
