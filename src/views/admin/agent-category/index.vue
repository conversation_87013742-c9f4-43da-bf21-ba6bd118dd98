<template>
  <CommonPage>
    <template #action>
      <div class="flex justify-between gap-2">
        <NButton type="primary" @click="initAll">
          <SvgIcon icon="ri:reset-left-line"></SvgIcon>
          刷新
        </NButton>
        <NButton type="primary" @click="handleAdd()">
          <SvgIcon icon="akar-icons:plus"></SvgIcon>
          创建分类
        </NButton>
      </div>
    </template>

    <!-- <AppCard class="mb-2 min-h-15 border rounded bg-gray-100	">
      <form class="flex justify-between p-4" @submit.prevent="handleSearch()">
        <n-scrollbar x-scrollable>
          <n-space :wrap="false" :size="[32, 16]" class="p-2.5">
            <MeQueryItem label="名称" :label-width="50">
              <n-input v-model:value="queryItems.name" type="text" placeholder="请输入名称" clearable />
            </MeQueryItem>
          </n-space>
        </n-scrollbar>
        <div class="flex-shrink-0 p-2.5">
          <n-button ghost type="primary" @click="handleReset">
            <SvgIcon icon="ri:reset-left-line"></SvgIcon>
            重置
          </n-button>
          <n-button attr-type="submit" class="ml-5" type="primary">
            <SvgIcon icon="lucide:search"></SvgIcon>
            搜索
          </n-button>
        </div>
      </form>
    </AppCard> -->
    <n-tabs type="line" animated v-model:value="activeTab">
      <n-tab-pane name="category" tab="智能体分类">
        <div class="mt-4"></div>
        <NDataTable :loading="loading" :scroll-x="scrollX" :columns="columns" :data="tableData"
          :row-key="(row) => row['id']" />
      </n-tab-pane>
      <n-tab-pane name="appCategory" tab="AI应用分类">
        <div class="mt-4"></div>
        <NDataTable :loading="loading" :scroll-x="scrollX" :columns="appColumns" :data="appTableData"
          :row-key="(row) => row[rowKey]" />
      </n-tab-pane>
    </n-tabs>


    <n-modal v-model:show="modalShow" class="modal-box" :preset="undefined" size="huge" :bordered="false"
      style="width: 520px;">
      <n-card @close="close()">
        <template #header>
          <header class="modal-header">
            {{ modalTitle }}
          </header>
        </template>
        <n-form :model="modalForm" ref="formRef" label-placement="left" label-align="left" :label-width="60">
          <n-form-item label="分类">
            <n-radio-group v-model:value="modalForm.categoryType" name="radiogroup" :disabled="actionType === 'edit'">
              <n-space>
                <n-radio v-for="item in categoryOptions" :key="item.key" :value="item.key">
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="名称" path="name" :rule="{
            required: true,
            message: '请输入名称',
            trigger: ['input', 'blur'],
          }">
            <n-input v-model:value="modalForm.name" type="text" placeholder="请输入名称" clearable />
          </n-form-item>
          <n-form-item v-if="modalForm.categoryType === 'category'" label="父分类" path="parentId">
            <!-- <n-select v-model:value="modalForm.parentId" :options="tableData" label-field="name" key-field="id"
              placeholder="请选择" clearable /> -->
            <n-tree-select v-model:value="modalForm.parentId" :options="tableData" label-field="name" key-field="id"
              placeholder="请选择" clearable />
          </n-form-item>
          <n-form-item v-if="modalForm.categoryType !== 'category'" label="图标">
            <n-select v-model:value="modalForm.icon" :options="iconOptions" clearable filterable />
          </n-form-item>
          <n-form-item label="排序" path="orderNum" :rule="{
            required: true,
            message: '请输入排序',
            trigger: ['input', 'blur'],
          }">
            <n-input-number v-model:value="modalForm.orderNum" />
          </n-form-item>
        </n-form>

        <!-- 底部按钮 -->
        <template #footer>
          <slot name="footer">
            <footer class="flex justify-end">
              <n-button @click="handleCancel()">
                取消
              </n-button>
              <n-button type="primary" class="ml-5" @click="handleSave()">
                保存
              </n-button>
            </footer>
          </slot>
        </template>
      </n-card>
    </n-modal>
  </CommonPage>
  <AgentPopup ref="agentPopupRef" @submit="agentSubmit"></AgentPopup>
  <AgentMovePopup ref="agentMovePopupRef" @submit="agentMoveSubmit"></AgentMovePopup>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { NButton, } from 'naive-ui'
import api from './api'
import { icons } from '@/utils'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import AgentMovePopup from "./AgentMovePopup.vue"
import { list2map } from '@/utils'

onMounted(() => {
  initAll()
})

const initAll = () => {
  initLinkList()
  initGroupList()
}

const activeTab = ref('category')
const agentMovePopupRef = ref()

const categoryOptions = [{
  label: '智能体分类',
  key: 'category'
}, {
  label: 'AI 应用分类',
  key: 'appCategory'
}]
const columns = [
  // {
  //   type: 'selection',
  // },
  { title: '名称', key: 'name', width: 80, ellipsis: { tooltip: true } },
  { title: '排序', key: 'orderNum', width: 40, ellipsis: { tooltip: true } },
  {
    title: '已绑定', key: 'agentIds', width: 380, ellipsis: { tooltip: true },
    // render(row) {
    //   return row.agents?.map((item) => item.name).join(";\n")
    // },
    render(row) {
      return [
        h(
          'div',
          {
            class: 'cursor-pointer flex justify-between items-center gap-2',
            onClick: () => showMove(row),
          },
          // row.agents?.map((item) => item.name).join(";\n")
          [h('p', { class: 'w-[360px] truncate' }, row.agents?.map((item) => item.name).join(";\n")), h(SvgIcon, { icon: 'fa-solid:sort' })],
        ),
      ]
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleOpenAgentPopup(row),
          },
          {
            default: () => '分配智能体',
            icon: () => h(SvgIcon, { icon: 'typcn:link-outline' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]
const appColumns = [
  // {
  //   type: 'selection',
  // },
  { title: '名称', key: 'name', width: 80, ellipsis: { tooltip: true } },
  {
    title: '已绑定', key: 'agents', width: 380, ellipsis: { tooltip: true },
    // render(row) {
    //   return row.agents?.map((item) => item.name).join(";\n")
    // },
    render(row) {
      if (!row.agents?.length || row.agents?.length === 1) {
        return [
          h('p', { class: 'w-[360px] truncate' }, row.agents?.map((item) => item.name).join(";\n")),
        ]
      }
      return [
        h(
          'div',
          {
            class: 'cursor-pointer flex justify-between items-center gap-2',
            onClick: () => showMove(row),
          },
          // row.agents?.map((item) => item.name).join(";\n")
          [h('p', { class: 'w-[360px] truncate' }, row.agents?.map((item) => item.name).join(";\n")), h(SvgIcon, { icon: 'fa-solid:sort' })],
        ),
      ]
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleOpenAgentPopup(row),
          },
          {
            default: () => '分配智能体',
            icon: () => h(SvgIcon, { icon: 'typcn:link-outline' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.linkId),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]

const tableData = ref([])
const appTableData = ref([])

const modalForm = ref({
  categoryType: 'category',
  orderNum: 1,
  parentId: ''
})

const iconOptions = icons.map(item => ({
  label: () =>
    h('span', { class: 'flex items-center gap-2' }, [h(SvgIcon, { icon: `${item}`, class: 'shrink-0' }), item]),
  value: item,
}))

const modalShow = ref(false)
const modalTitle = ref('')
const initLinkList = () => {
  api.linkList().then((res) => {
    appTableData.value = res.data
  })
}
const initGroupList = () => {
  api.groupList().then((res) => {
    res.data.forEach(item => {
      item.agents = item.agents.sort((a, b) => a.orderNum - b.orderNum)
    })
    tableData.value = res.data
  })
}

const actionType = ref('')
const handleOpen = ({ title, row, action, categoryType = "category" }) => {
  modalShow.value = true
  modalTitle.value = title
  actionType.value = action
  modalForm.value = {
    categoryType,
    ...row,
  }
  modalForm.value.parentId = modalForm.value.parentId || ''
}

const handleAdd = () => {
  handleOpen({
    action: 'add',
    title: activeTab.value === 'category' ? '创建分类' : '创建应用分类',
    row: { orderNum: 1 },
    onOk: handleSave,
  })
}

const handleEdit = (row) => {
  handleOpen({
    action: 'edit',
    title: activeTab.value === 'category' ? '编辑分类' : '编辑应用分类',
    row,
    categoryType: activeTab.value,
    onOk: handleSave,
  })
}
const handleCancel = () => {
  modalShow.value = false
}

const handleSave = () => {

  let func = undefined
  if (modalForm.value.categoryType === 'category') {
    if (actionType.value == 'add') {
      func = api.groupAdd
    } else {
      func = api.groupUpdate
    }
  } else {
    if (actionType.value == 'add') {
      func = api.linkAdd
    } else {
      func = api.linkUpdate
    }
  }
  const req = {
    ...modalForm.value,
  }
  delete req.categoryType
  func(req).then(() => {
    $message.success('保存成功')
    modalShow.value = false
    if (modalForm.value.categoryType === 'category') initGroupList()
    else initLinkList()
  })
}
const handleDelete = (id) => {
  const func = activeTab.value === 'category' ? api.groupDelete : api.linkDelete
  func(id).then(() => {
    $message.success('删除成功')
    if (activeTab.value === 'category') initGroupList()
    else initLinkList()
  })
}

const agentPopupRef = ref(null)
const currentRow = ref(null)
const handleOpenAgentPopup = (row) => {
  currentRow.value = row
  agentPopupRef.value.show(currentRow.value.agents?.map(item => item.id) || [])
}

const agentSubmit = (ids) => {
  let func = undefined
  // ids 混合
  // currentRow.value.agents 中存在的取 agents数据，不存在的取 ids数据
  let agents = []
  const agentMap = list2map(currentRow.value.agents || [], 'id')
  ids.forEach(agentId => {
    const agent = agentMap[agentId]
    if (agent) {
      agents.push({
        agentId: agent.id,
        orderNum: agent.orderNum,
      })
    } else {
      agents.push({ agentId, orderNum: 9999 })
    }
  })
  if (currentRow.value.agents) {
  }

  let req = {}
  if (currentRow.value.linkId) {
    func = api.linkAddAgent
    req = {
      linkId: currentRow.value.linkId,
      agents,
    }
  } else {
    func = api.groupAddAgent
    req = {
      groupId: currentRow.value.id,
      agents,
    }
  }
  func(req).then(() => {
    $message.success('添加成功')
    agentPopupRef.value.close()
    initAll()
  })
}

const showMove = (row) => {
  currentRow.value = row
  agentMovePopupRef.value?.show(row)
}
const agentMoveSubmit = (agentList) => {
  const agents = []
  agentList.forEach((item, index) => {
    item.orderNum = index + 1
    agents.push({
      agentId: item.id,
      orderNum: index + 1
    })
  })
  api.updateOrder({
    groupId: currentRow.value.id,
    agents,
  })
}
</script>
