<!-- agent 弹窗选择组件 -->
<template>
  <n-modal v-model:show="modalShow" :preset="dialog" size="huge" :bordered="false"
    style="width: 500px;max-height: 80vh;" class="overflow-hidden">
    <n-card @close="close()">
      <template #header>
        <header class="modal-header">
          智能体排序
        </header>
      </template>
      <div class="modal-body flex flex-col  gap-2 overflow-auto">
        <draggable class="dragArea flex flex-col  gap-2" :list="agentList" v-bind="dragOptions" @change="dragChange">
          <transition-group type="transition" name="flip-list">
            <div class="item border hover:bg-gray-100 p-2 rounded-lg flex items-center justify-between cursor-pointer"
              v-for="item in agentList" :key="item.id">
              <div class="flex items-center gap-2">
                <n-avatar :src="item.icon" :renderFallback="renderFallback(item.name)" round />
                <span>{{ item.name }}</span>
              </div>
              <svg-icon icon="ix:drag-gripper" height="20" width="20" @click="" />
            </div>
          </transition-group>
        </draggable>
      </div>
      <template #footer>
        <footer class="modal-footer w-full flex justify-end gap-2">
          <n-button @click="close()">取消</n-button>
          <n-button type="primary" @click="submit()">确定</n-button>
        </footer>
      </template>
    </n-card>
  </n-modal>
</template>
<script setup>
import { ref, computed, h } from 'vue'
import { NButton } from 'naive-ui'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import { VueDraggableNext as draggable } from 'vue-draggable-next'


const modalShow = ref()
const agentList = ref([])

const renderFallback = (name) => {
  return () => h('div', { class: "flex justify-center items-center justify-center w-full h-full text-xs text-[#ffffff] rounded-full bg-[#749BFF]" }, name.slice(0, 2))
}

const dragOptions = computed(() => {
  return {
    animation: 0,
    group: 'description',
    disabled: false,
    ghostClass: 'ghost',
  }
})

const show = ({ agents }) => {
  modalShow.value = true
  agentList.value = agents
}
const dragChange = (e) => {
  console.log(e)
}

const close = () => {
  modalShow.value = false
}

const emit = defineEmits(['submit'])
const submit = () => {
  modalShow.value = false
  emit('submit', agentList.value)
}

defineExpose({
  show,
  close,
})
</script>
<style lang="scss" scoped>
.modal-body {
  max-height: calc(80vh - 170px);
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.flip-list-move {
  transition: transform 0.5s;
}
</style>