
import { request } from '@/utils'

export default {
  // AI 应用分类
  linkAdd: data => request.post('/v2/system/link/add', data),
  linkList: (data = {}) => request.post('/v2/system/link/list', data),
  linkUpdate: data => request.post(`/v2/system/link/update`, data),
  linkDelete: (linkId) => request.get(`/v2/system/link/delete`, { params: { linkId } }),
  linkAddAgent: data => request.post('/v2/system/link/addAgent', data),

  // 智能体分类
  groupAddAgent: data => request.post('/v2/system/group/addAgent', data),
  groupAdd: data => request.post('/v2/system/group/add', data),
  groupList: (data = {}) => request.post('/v2/system/group/list', data),
  groupUpdate: data => request.post(`/v2/system/group/update`, data),
  groupDelete: (groupId) => request.get(`/v2/system/group/delete`, { params: { groupId } }),

  getAllApps: (data = {}) => request.post('/v2/system/apps/list', data),

  // 更新排序 { "agents":[], "groupId":'' }
  updateOrder: data => request.post('/v2/system/group/updateOrder', data),

}
