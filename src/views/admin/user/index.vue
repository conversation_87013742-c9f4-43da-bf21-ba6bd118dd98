<template>
  <CommonPage>
    <template #action>
      <!-- <NButton v-permission="'AddUser'" type="primary" @click="handleAdd()"> -->
      <div class="flex gap-2">
        <!-- <n-upload class="mx-auto w-full text-center" :show-file-list="false" accept=".xlsx,.xls"
          @before-upload="onBeforeUpload">
          <NButton type="primary">
            <SvgIcon icon="uil:import"></SvgIcon>
            用户导入
          </NButton>
        </n-upload>

        <NButton type="primary" @click="handleExport">
          <SvgIcon icon="uil:export"></SvgIcon>
          导出
        </NButton> -->
        <NButton type="primary" @click="handleAdd()">
          <SvgIcon icon="akar-icons:plus"></SvgIcon>
          创建新用户
        </NButton>
      </div>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read"
      @post-processing="postProcessing">
      <MeQueryItem label="用户名" :label-width="50">
        <n-input v-model:value="queryItems.username" type="text" placeholder="请输入用户名" clearable />
      </MeQueryItem>
      <MeQueryItem label="手机号" :label-width="50">
        <n-input v-model:value="queryItems.phone" type="text" placeholder="请输入手机号" clearable>
        </n-input>
      </MeQueryItem>
      <MeQueryItem label="邮箱" :label-width="50">
        <n-input v-model:value="queryItems.email" type="text" placeholder="请输入邮箱" clearable>
        </n-input>
      </MeQueryItem>
      <MeQueryItem label="公司" :label-width="50">
        <n-select v-model:value="queryItems.companyId" clearable :options="comapnyOptions" label-field="name"
          value-field="id" />
      </MeQueryItem>

      <MeQueryItem label="状态" :label-width="50">
        <n-select v-model:value="queryItems.status" clearable :options="[
          { label: '启用', value: 'active' },
          { label: '停用', value: 'inactive' },
        ]" />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
        :disabled="modalAction === 'view'" :rules="rules">
        <n-form-item label="用户名" path="username">
          <n-input v-model:value="modalForm.username" placeholder="限制英文数字" :disabled="modalAction !== 'add'" />
        </n-form-item>
        <n-form-item label="昵称" path="nickname" :rule="{
          required: true,
          message: '请输入昵称',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.nickname" />
        </n-form-item>
        <n-form-item label="手机号" path="phone">
          <n-input v-model:value="modalForm.phone" :maxlength="11">
          </n-input>
        </n-form-item>
        <n-form-item label="邮箱" path="email">
          <n-input v-model:value="modalForm.email" />
        </n-form-item>
        <n-form-item label="头像">
          <div v-if="modalForm.head" class="icon-box relative w-[200px] h-[100px]">
            <n-image :src="modalForm.head" class="rounded-lg" object-fit="contain" />
            <div
              class="mask absolute top-0 left-0 h-full w-full rounded-lg bg-black/50 flex items-center justify-center">
              <SvgIcon icon="mi:delete" color="#fff" hoverColor="#c3c3c3" @click="modalForm.head = ''"></SvgIcon>
            </div>
          </div>
          <FileUpload v-else @on-success="onSuccess" :max-size="1"></FileUpload>
        </n-form-item>
        <n-form-item label="公司" path="companyId">
          <n-select v-model:value="modalForm.companyId" clearable :options="comapnyOptions" label-field="name"
            value-field="id" />
        </n-form-item>
        <n-form-item :span="12" label="所属部门" path="deptId">
          <n-tree-select v-model:value="modalForm.deptId" :options="deptOptions" label-field="deptName" key-field="id"
            placeholder="请选择" clearable />
        </n-form-item>
        <n-form-item label="角色" path="roleIds">
          <n-select v-model:value="modalForm.roleIds" clearable :options="roleOptionsFilter" label-field="roleName"
            value-field="id" />
        </n-form-item>

        <n-form-item v-if="['add', 'reset'].includes(modalAction)" :label="modalAction === 'reset' ? '重置密码' : '初始密码'"
          path="password">
          <n-input v-model:value="modalForm.password" :maxlength="8"/>
        </n-form-item>


        <n-form-item label="状态">
          <NSwitch v-model:value="modalForm.status" checked-value="active" unchecked-value="inactive">
            <template #checked>
              启用
            </template>
            <template #unchecked>
              停用
            </template>
          </NSwitch>
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NAvatar, NButton, NSwitch, NTag } from 'naive-ui'
import api from './api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import { ref, render, watch } from 'vue'

defineOptions({ name: 'UserMgt' })

const $table = ref(null)
const queryItems = ref({})

const roles = ref([])
api.getAllRoles().then(({ data = [] }) => (roles.value = data))

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleEdit,
  handleDelete,
  handleOpen,
  handleSave,
} = useCrud({
  name: '用户',
  initForm: { status: 'active', password: '1qaz@WSX' },
  doCreate: (formData) => {
    // 将单选的 roleIds 转换为数组格式
    const data = { ...formData }
    if (data.roleIds && !Array.isArray(data.roleIds)) {
      data.roleIds = [data.roleIds]
    }
    return api.create(data)
  },
  doDelete: api.delete,
  doUpdate: (formData) => {
    // 将单选的 roleIds 转换为数组格式
    const data = { ...formData }
    if (data.roleIds && !Array.isArray(data.roleIds)) {
      data.roleIds = [data.roleIds]
    }
    if (!data.companyId) {
      data.deptId = ''
    }
    return api.update(data)
  },
  refresh: () => $table.value?.handleSearch(),
})
const rules = ref({
  username: [
    {
      required: true,
      validator(_, value) {
        if (value === '') {
          return new Error('请输入用户名')
        }
        // 限制英文数字，长度 6 - 16 位
        if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
          return new Error('用户名必须为英文数字，长度 6～16 位')
        }
        return true
      },
      trigger: ['blur'],
    }
  ],
  phone: {
    required: true,
    trigger: ['input', 'blur'],
    message: '请输入正确手机号',
    validator: (_, value) => {
      return /^[1]+\d{10}$/.test(value)
    },
  },
  email: [
    {
      required: true,
      validator(_, value) {
        if (value === '') {
          return new Error('请输入邮箱')
        }
        // 邮箱格式
        if (!/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
          return new Error('邮箱格式错误')
        }
        return true
      },
      trigger: ['input', 'blur'],
    }
  ],
  password: [
    {
      required: true,
      validator(rule, value) {
        if (value === '') {
          return new Error('请输入密码')
        }
        // 密码必须为字母数字符号，长度 8 位
        if (!/^[a-zA-Z0-9@#$%&*^_-]{8,}$/.test(value)) {
          return new Error('密码必须为字母数字，长度 8 位')
        }
        return true
      },
      trigger: ['input', 'blur'],
    }
  ],
  roleIds: [
    {
      required: true,
      validator(rule, value) {
        if (!value) {
          return new Error('请选择角色')
        }
        return true
      },
      trigger: ['change', 'blur'],
    }
  ],
})
const columns = [
  {
    title: '头像',
    key: 'head',
    width: 80,
    render: ({ head }) =>
      h(NAvatar, {
        size: 'medium',
        src: head,
        round: true,
      }),
  },
  { title: '用户名', key: 'username', width: 100, ellipsis: { tooltip: true } },
  { title: '昵称', key: 'nickname', width: 100, ellipsis: { tooltip: true } },
  {
    title: '公司',
    key: 'companyId',
    width: 200,
    ellipsis: { tooltip: true },
    render: ({ companyId }) => {
      if (companyId) {
        const comapny = comapnyOptions.value.find(item => item.id == companyId)
        return [h(
          NTag,
          { type: 'info' },
          { default: () => comapny?.name || '-' },
        )]
      }
      return '-'
    },
  },
  {
    title: '角色',
    key: 'roles',
    width: 200,
    ellipsis: { tooltip: true },
    render: ({ roles }) => {
      if (roles?.length) {
        return roles.map((item, index) =>
          h(
            NTag,
            { type: 'success', style: index > 0 ? 'margin-left: 8px;' : '' },
            { default: () => item.roleName },
          ),
        )
      }
      return '暂无角色'
    },
  },
  { title: '邮箱', key: 'email', width: 150, ellipsis: { tooltip: true } },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 180,
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    render: row =>
      h(
        NSwitch,
        {
          size: 'small',
          rubberBand: false,
          value: row.status == 'active',
          loading: !!row.enableLoading,
          onUpdateValue: () => handleEnable(row),
        },
        {
          checked: () => '启用',
          unchecked: () => '停用',
        },
      ),
  },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handlePassword(row),
          },
          {
            default: () => '重置密码',
            icon: () => h(SvgIcon, { icon: 'ri:reset-left-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]

const postProcessing = async (list) => {
  list.forEach((item) => {
    // 从接口获取的角色数组，转换为单选值（取第一个角色的ID）
    const roleIds = item.roles?.map(role => role.id) || []
    item.roleIds = roleIds.length > 0 ? roleIds[0] : null
  })
}

const comapnyOptions = ref([])
const roleOptions = ref([])
let roleOptionsFilter = ref([])

onMounted(async () => {
  api.getAllCompanys().then(({ data = [] }) => (comapnyOptions.value = data))
  api.getAllRoles().then(({ data = [] }) => {
    roleOptions.value = data
    roleOptionsFilter.value = data.filter(item => {
      return item.status == '0'
    })
  })
  $table.value?.handleSearch()
})

// 监听公司变化，清空部门并重新获取部门列表
watch(() => modalForm.value.companyId, (newCompanyId, oldCompanyId) => {
  // 当公司发生变化时，清空部门选择
  if (newCompanyId !== oldCompanyId) {
    modalForm.value.deptId = null
  }
  // 重新获取新公司的部门列表
  initDept()
})

// 监听弹框打开，初始化部门列表
watch(() => modalRef.value?.show, (isShow) => {
  if (isShow) {
    // 弹框打开时，根据当前选择的公司初始化部门列表
    initDept()
  }
})
const deptOptions = ref([])
const initDept = () => {
  // 如果没有选择公司，清空部门选项
  if (!modalForm.value.companyId) {
    deptOptions.value = []
    return
  }

  api.getDeptByCompanyId({
    companyId: modalForm.value.companyId
  }).then(({ data = [] }) => {
    deptOptions.value = data
  }).catch(() => {
    // 如果获取部门失败，也清空部门选项
    deptOptions.value = []
  })
}

const onSuccess = (data) => {
  modalForm.value.head = data.url
}


async function handleEnable(row) {
  row.enableLoading = true
  row.status = row.status == 'active' ? 'inactive' : 'active'
  row.roleIds = [row.roleIds]
  try {
    await api.update(row)
    row.enableLoading = false
    $message.success('操作成功')
    $table.value?.handleSearch()
  }
  catch (error) {
    console.error(error)
    row.enableLoading = false
  }
}

function handlePassword(row) {
  $dialog.warning({
    content: `确定要重置 ${row.nickname} 密码吗？`,
    title: '提示',
    positiveText: '确定',
    negativeText: '取消',
    async onPositiveClick() {
      api.resetPassword(row.id).then(() => {
        $message.success('操作成功')
        $table.value?.handleSearch()
      })
    }
  })
}



// 用户导入
const onBeforeUpload = ({ file, props }) => {

  if (!file || !file.type) {
    $message.error('请选择文件')
  }
  if (file.file.size > 5 * 1024 * 1024) {
    $message.error(`文件大小不能超过5MB`)
    return false
  }
  $message.success('上传中...')
  const formData = new FormData()
  formData.append('file', file.file)
  api.import(formData).then(({ data }) => {
    // data length > 0 说明错误数据
    if (data.length > 0) {
      $message.error(data.join('\n'), {
        duration: 0,
        closable: true,
      })
    } else {
      $message.success('上传成功')
    }
    $table.value?.handleSearch()
  }).catch((err) => {
    $message.error('上传失败')
    console.log(err)
  }).finally(() => {
  })
  return true
}

// 用户导出
const handleExport = () => {
  api.exportUser(queryItems.value).then((data) => {
    console.log(data);
    const url = window.URL.createObjectURL(new Blob([data]))
    // const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    // const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', 'accounts.xlsx')
    document.body.appendChild(link)
    link.click()
  })
}
</script>
