
import { request } from '@/utils'

export default {
  create: data => request.post('/v2/system/user/add', data),
  read: (data) => request.post('/v2/system/user/list', data),
  update: data => request.post(`/v2/system/user/update`, data),
  import: data => request.post(`/v2/system/user/import`, data),
  // 导出
  exportUser: data => request.post(`/v2/system/user/export`, data, { responseType: 'blob' }),
  delete: userId => request.get(`/v2/system/user/delete`, { params: { userId } }),
  // 1qaz@WSX
  resetPassword: userId => request.get(`/v2/system/user/resetPassword`, { params: { userId } }),

  getAllCompanys: (data = {}) => request.post('/v2/system/company/list', data),
  getDeptByCompanyId: (data = {}) => request.post('/v2/system/dept/list', data),
  getAllRoles: (data = {}) => request.post('/v2/system/role/list', data),
}
