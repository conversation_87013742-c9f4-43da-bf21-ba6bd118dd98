<template>
  <CommonPage>
    <template #action>
      <NButton type="primary" @click="handleAdd()">
        <SvgIcon icon="akar-icons:plus"></SvgIcon>
        创建智能体
      </NButton>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns"
      :get-data="$api.getAllAgents">
      <!-- <MeQueryItem label="id" :label-width="50">
        <n-input v-model:value="queryItems.id" type="text" placeholder="请输入部门名" clearable />
      </MeQueryItem> -->
      <MeQueryItem label="名称" :label-width="50">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入智能体名" clearable />
      </MeQueryItem>
      <MeQueryItem label="来源" :label-width="50">
        <n-select v-model:value="queryItems.type" :options="dictionaryListOptions" label-field="value"
          value-field="key" />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
        :disabled="modalAction === 'view'">
        <n-form-item label="来源" path="type">
          <n-select v-model:value="modalForm.type" :options="dictionaryListOptions.filter(item => item.key !== 'dify')"
            label-field="value" value-field="key" />
        </n-form-item>
        <n-form-item label="名称" path="name" :rule="{
          required: true,
          message: '请输入名称',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.name" />
        </n-form-item>
        <n-form-item label="图标">
          <div v-if="modalForm.icon" class="icon-box relative w-[100px] h-[100px] flex justify-center items-center">
            <n-image :src="modalForm.icon" class="rounded-lg" object-fit="contain" />
            <div
              class="mask absolute top-0 left-0 h-full w-full rounded-lg bg-black/50 flex items-center justify-center">
              <SvgIcon icon="mi:delete" color="#fff" hoverColor="#c3c3c3" @click="modalForm.icon = ''"></SvgIcon>
            </div>
          </div>
          <FileUpload v-else @on-success="onSuccess" :max-size="1"></FileUpload>
        </n-form-item>
        <n-form-item label="链接" path="url" :rule="{
          required: true,
          message: '请输入名称',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.url" />
        </n-form-item>
        <n-form-item label="描述" path="description" :rule="{
          required: true,
          message: '请输入名称',
          trigger: ['input', 'blur'],
        }">
          <n-input type="textarea" v-model:value="modalForm.description" />
        </n-form-item>
      </n-form>
    </MeModal>
    <AgentPopup ref="agentPopupRef"></AgentPopup>
  </CommonPage>
</template>

<script setup>
import { ref, computed, watch, } from 'vue'
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { NAvatar, NButton, NSwitch, NTag } from 'naive-ui'
import api from './api'
import $api from '@/api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import AgentPopup from '@/components/agent-popup/index.vue'



defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
  queryDictionaryList()
})

// 获取字典列表
let dictionaryListOptions = ref([])
const queryDictionaryList = async () => {
  let { data } = await $api.dictionaryList({
    type: 'agentSource'
  })
  dictionaryListOptions.value = data
}

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleEdit,
  handleView,
  handleDelete,
  handleOpen,
  // handleSave,
} = useCrud({
  name: '智能体',
  initForm: { type: 'fastGpt' },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: () => $table.value?.handleSearch(),
})


const columns = [
  // {
  //   type: 'selection',
  // },
  {
    title: '图标', key: 'icon', width: 80, ellipsis: { tooltip: true },
    render(row) {
      return h(
        NAvatar,
        {
          size: 'small',
          round: true,
          src: row.icon,
        },
        {
          fallback: () => h('div', { class: "flex justify-center items-center justify-center w-full h-full text-xs text-[#ffffff] rounded-full bg-[#749BFF]" }, row.name.slice(0, 2)),
        }
      )
    }
  },
  { title: '名称', key: 'name', ellipsis: { tooltip: true } },
  { title: '来源', key: 'type', ellipsis: { tooltip: true } },
  // { title: '模型', key: 'mode', ellipsis: { tooltip: true } },
  { title: '创建时间', key: 'createAt', ellipsis: { tooltip: true } },
  { title: '描述', key: 'description', ellipsis: { tooltip: true } },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      if (row.type === 'dify') {
        return [
          h(
            NButton,
            {
              size: 'small',
              style: 'margin-left: 12px;',
              onClick: () => handleView(row),
            },
            '查看'
          )
        ]
      }
      return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]


watch(() => modalForm.value.companyId, () => {
  initDept()
})
const deptData = ref([])
const initDept = () => {
  api.read({
    companyId: modalForm.value.companyId
  }).then(({ data = [] }) => {
    deptData.value = data
  })
}


const handleSave = async (row) => {

}

function handleOpenRolesSet(row) {
  const roleIds = row.roles.map(item => item.id)
  handleOpen({
    action: 'setRole',
    title: '分配角色',
    row: { id: row.id, username: row.username, roleIds },
    onOk: onSave,
  })
}

function onSave() {
  if (modalAction.value === 'setRole') {
    return handleSave({
      api: () => api.update(modalForm.value),
      cb: () => $message.success('分配成功'),
    })
  }
  else if (modalAction.value === 'reset') {
    return handleSave({
      api: () => api.resetPwd(modalForm.value.id, modalForm.value),
      cb: () => $message.success('密码重置成功'),
    })
  }
  handleSave()
}

const onSuccess = (data) => {
  modalForm.value.icon = data.url
}
</script>
