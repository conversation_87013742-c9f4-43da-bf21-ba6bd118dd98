
import { request } from '@/utils'

export default {
  create: data => request.post('/v2/system/agent/add', data),
  readById: (params = {}) => request.get(`/v2/system/agent/getById/`, { params }),
  update: data => request.post(`/v2/system/agent/update`, data),
  delete: (agentId) => request.get(`/v2/system/agent/delete`, { params: { agentId } }),

  getAllCompanys: (data = {}) => request.post('/v2/system/company/list', data),
  
}
