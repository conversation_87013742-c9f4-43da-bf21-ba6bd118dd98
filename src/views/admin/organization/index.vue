<template>
  <CommonPage>
    <template #action>
      <NButton type="primary" @click="handleAdd()">
        <SvgIcon icon="akar-icons:plus"></SvgIcon>
        创建公司
      </NButton>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="名称" :label-width="50">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入公司名" clearable />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
        :disabled="modalAction === 'view'">
        <n-form-item label="公司名称" path="name" :rule="{
          required: true,
          message: '请输入公司名称',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.name" />
        </n-form-item>
        <n-form-item label="公司简介" path="description" :rule="{
          required: true,
          message: '请输入公司简介',
          trigger: ['input', 'blur'],
        }">
          <n-input type="textarea" v-model:value="modalForm.description" />
        </n-form-item>

        <n-form-item label="公司图标" path="icon" :rule="{
          required: true,
          message: '请上传公司logo',
          trigger: ['input', 'blur'],
        }">
          <div v-if="modalForm.icon" class="icon-box relative w-[200px] h-[100px]">
            <n-image :src="modalForm.icon" class="rounded-lg" object-fit="contain" />
            <div
              class="mask absolute top-0 left-0 h-full w-full rounded-lg bg-black/50 flex items-center justify-center">
              <SvgIcon icon="mi:delete" color="#fff" hoverColor="#c3c3c3" @click="modalForm.icon = ''"></SvgIcon>
            </div>
          </div>
          <FileUpload v-else @on-success="onSuccess" :max-size="1"></FileUpload>
        </n-form-item>

      </n-form>
    </MeModal>
    <!-- <cropper-canvas background>
      <cropper-image src="https://fengyuanchen.github.io/cropperjs/picture.jpg" alt="Picture"
        translatable></cropper-image>
      <cropper-shade hidden></cropper-shade>
      <cropper-selection initial-coverage="0.5" resizable width="100" height="100">
        <cropper-grid role="grid" covered></cropper-grid>
        <cropper-crosshair centered></cropper-crosshair>
        <cropper-handle action="move" theme-color="rgba(255, 255, 255, 0.35)"></cropper-handle>
      </cropper-selection>
    </cropper-canvas> -->
    <AgentBindPopup ref="agentBindPopupRef" @submit="submit"></AgentBindPopup>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NImage, NButton, NSwitch, NTag } from 'naive-ui'
import api from './api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import { useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import AgentBindPopup from '@/components/agent-bind-popup/index.vue'
// import 'cropperjs';
// import { repeat } from 'seemly'
import { ref } from 'vue'

const $table = ref(null)
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleEdit,
  handleDelete,
  handleOpen,
  handleSave,
} = useCrud({
  name: '公司',
  initForm: { enable: true, icon: '' },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: () => $table.value?.handleSearch(),
})


const columns = [
  // {
  //   type: 'selection',
  // },
  {
    title: '图标',
    key: 'icon',
    width: 100,
    render: ({ icon }) =>
      h(NImage, {
        size: 'medium',
        class: 'flex justify-center items-center',
        src: icon,
        'img-props': { class: 'h-[25px] object-cover' }
      }),
  },
  { title: '名称', key: 'name', width: 150, ellipsis: { tooltip: true } },
  { title: '简介', key: 'description', width: 150, ellipsis: { tooltip: true } },
  {
    title: '创建时间',
    key: 'createAt',
    width: 180,
  },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]

const currentRow = ref(null)
const agentBindPopupRef = ref(null)
async function handleBind(row) {
  currentRow.value = row
  agentBindPopupRef.value.show(row.id)
}

const router = useRouter()
const appStore = useAppStore()

const onSuccess = (data) => {
  modalForm.value.icon = data.url
}
const handlePreview = (row) => {
  appStore.setPreviewCompanyId(row.id)
  router.push({ path: '/agents' })
}
const submit = async (data) => {
  // 分类绑定
  if (data.tabIndex == 1) {
    api.bindGroup({
      companyId: currentRow.value.id,
      groupIds: data.checkedKeys
    }).then(() => {
      $message.success('绑定成功')
      agentBindPopupRef.value.close()
    })
  } else if (data.tabIndex == 2) {
    api.bindLink({
      companyId: currentRow.value.id,
      linkIds: data.checkLinkIds
    }).then(() => {
      $message.success('绑定成功')
      agentBindPopupRef.value.close()
    })
  } else if (data.tabIndex == 3) {
    api.bindAgent({
      companyId: currentRow.value.id,
      agentIds: data.checkAgentIds
    }).then(() => {
      $message.success('绑定成功')
      agentBindPopupRef.value.close()
    })
  }
}


</script>
<style lang="scss" scoped>
.icon-box {
  .mask {
    display: none;
  }

  &:hover {
    .mask {
      display: flex;
    }
  }
}
</style>
