
import { request } from '@/utils'

export default {
  create: data => request.post('/v2/system/company/add', data),
  readById: (companyId) => request.get(`/v2/system/company/getById/`, { params: { companyId } }),
  read: (data = {}) => request.post('/v2/system/company/list', data),
  update: data => request.post(`/v2/system/company/update`, data),
  delete: (companyId) => request.get(`/v2/system/company/delete`, { params: { companyId } }),

  bindAgent: data => request.post(`/v2/system/company/bindAgent`, data),
  bindGroup: data => request.post(`/v2/system/company/bindGroup`, data),
  bindLink: data => request.post(`/v2/system/company/bindLink`, data),
}
