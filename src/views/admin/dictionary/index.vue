<template>
  <CommonPage>
    <template #action>
      <NButton type="primary" @click="handleAdd()">
        <SvgIcon icon="akar-icons:plus"></SvgIcon>
        新增字典
      </NButton>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="字段值" :label-width="80">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入字段值" clearable />
      </MeQueryItem>
    </MeCrud>
    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm">
        <n-form-item label="名称" path="type" :rule="{
          required: true,
          message: '请输入字典名称',
          trigger: ['input', 'blur'],
        }">
          <!-- <n-select v-model:value="modalForm.type" clearable :options="dirtionaryOptions" /> -->
          <n-input v-model:value="modalForm.type" placeholder="请输入字典名称" />
        </n-form-item>
        <n-form-item label="字段值" path="key" :rule="{
          required: true,
          message: '请输入字段值',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.key" />
        </n-form-item>
        <n-form-item label="字段名" path="value" :rule="{
          required: true,
          message: '请输入字段名',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.value" />
        </n-form-item>
        <n-form-item label="描述" path="description" :rule="{
          required: true,
          message: '请输入字典描述',
          trigger: ['input', 'blur'],
        }">
          <n-input type="textarea" v-model:value="modalForm.description" />
        </n-form-item>

      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { NButton, NSwitch } from 'naive-ui'
import { demandDirtionaryOptions, mapDirtionaryLabel } from '@/options/demand.js'
import api from './api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"

defineOptions({ name: 'RoleMgt' })

const router = useRouter()

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

const { modalRef, modalFormRef, modalAction, modalForm, handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '字典',
    doCreate: api.create,
    doDelete: api.delete,
    doUpdate: api.update,
    refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
  })

const columns = [
  {
    title: '名称',
    key: 'type',
    render(row) {
      return h('span', row.type)
    },
  },
  { title: '字段值', key: 'key' },
  { title: '字段名', key: 'value' },
  { title: '描述', key: 'description', width: 200, ellipsis: { tooltip: true } },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'right',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-left: 12px;',
            disabled: row.code === 'SUPER_ADMIN',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            disabled: row.code === 'SUPER_ADMIN',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]

</script>
