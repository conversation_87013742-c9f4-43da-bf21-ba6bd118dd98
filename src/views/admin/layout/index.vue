<template>
  <n-layout class="h-full w-full" has-sider>
    <n-layout-sider class="h-full w-full" bordered show-trigger collapse-mode="width" :collapsed-width="64" :width="240"
      :native-scrollbar="false" :inverted="inverted">
      <n-menu :inverted="inverted" v-model:value="activeMenu" :collapsed-width="64" :collapsed-icon-size="22"
        :options="menuOptions" class="text-[#26334b]" :style="{
          '--n-item-text-color': '#26334b !important'
        }" :on-update:value="handleMenuChange" />
    </n-layout-sider>
    <n-layout-content class="h-full w-full">
      <RouterView></RouterView>
    </n-layout-content>
  </n-layout>
</template>
<script setup>
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import { h, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePermissionStore } from '@/store/modules/permission'

const route = useRoute()
const router = useRouter()
const permissionStore = usePermissionStore()

const activeMenu = ref('')

watch(() => route.path, (val) => {
  activeMenu.value = val
}, { immediate: true })

function renderIcon(icon) {
  // 增加 icon 属性
  return () => h(SvgIcon, { icon })
}

const menuOptions = computed(() => {
  const children = permissionStore.menus.find(item => item.code === 'Admin').children
  return children.map(item => {
    return {
      label: item.menuName,
      key: item.path,
      icon: renderIcon(item.icon)
    }
  })
})

// permissionStore.menus
// const menuOptions = [
//   {
//     label: '公司管理',
//     key: '/admin/organization',
//     icon: renderIcon('mdi:company')
//   },
//   {
//     label: '部门管理',
//     key: '/admin/department',
//     icon: renderIcon('fluent:organization-16-regular')
//   },
//   {
//     label: '用户管理',
//     key: '/admin/user',
//     icon: renderIcon('solar:user-linear')
//   },
//   {
//     label: '角色管理',
//     key: '/admin/role',
//     icon: renderIcon('carbon:user-role')
//   },
//   {
//     label: '导航管理',
//     key: '/admin/resource',
//     icon: renderIcon('fluent:navigation-32-regular')
//   },
//   {
//     label: '字典管理',
//     key: '/admin/dictionary',
//     icon: renderIcon('streamline:dictionary-language-book')
//   },
//   {
//     label: '智能体管理',
//     key: '/admin/agent/category',
//     icon: renderIcon('mage:robot')
//   },

// ]

const search = ref('')
const handleMenuChange = (val) => {
  console.log(val)
  router.push(val)
}
</script>
<style lang="scss" scoped>
@media (min-width: 750px) {
  .fsl\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1024px) {
  .lg\:block {
    display: block !important
  }

  .lg\:flex {
    display: flex !important
  }

  .lg\:px-8 {
    padding-left: 2rem !important;
    padding-right: 2rem !important
  }
}

@media (min-width: 1250px) {
  .fxl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important
  }

  .fxl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1280px) {
  .xl\:flex {
    display: flex !important
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1680px) {
  .fxl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important
  }

  .fxl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1960px) {
  .fxl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important
  }
}
</style>