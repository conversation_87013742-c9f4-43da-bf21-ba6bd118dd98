import { request } from '@/utils'

export default {
  create: data => request.post('/v2/system/role/add', data),
  read: (data = {}) => request.post('/v2/system/role/list', data),
  update: data => request.post(`/v2/system/role/update`, data),
  delete: id => request.get(`/v2/system/role/delete?roleId=${id}`),

  getAllPermissionTree: () => request.post('/v2/system/menu/list', {}),
  getAllUsers: (params = {}) => request.get('/v2/user', { params }),
  addRoleUsers: (roleId, data) => request.patch(`/v2/role/users/add/${roleId}`, data),
  removeRoleUsers: (roleId, data) => request.patch(`/v2/role/users/remove/${roleId}`, data),
}
