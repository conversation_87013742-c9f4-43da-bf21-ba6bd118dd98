<template>
  <CommonPage>
    <template #action>
      <NButton type="primary" @click="handleAdd()">
        <SvgIcon icon="akar-icons:plus"></SvgIcon>
        新增角色
      </NButton>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="角色名" :label-width="50">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入角色名" clearable />
      </MeQueryItem>
      <MeQueryItem label="状态" :label-width="50">
        <n-select v-model:value="queryItems.status" clearable :options="[
          { label: '启用', value: 0 },
          { label: '停用', value: 1 },
        ]" />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
        :disabled="modalAction == 'view'">
        <n-form-item label="角色名" path="roleName" 
        :rule="{
          required: true,
          message: '请输入角色名',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.roleName" />
        </n-form-item>
        <n-form-item label="状态" path="status">
          <NSwitch v-model:value="modalForm.status" checked-value="0" unchecked-value="1">
            <template #checked>
              是
            </template>
            <template #unchecked>
              否
            </template>
          </NSwitch>
        </n-form-item>
        <n-form-item label="权限菜单" path="menuIds"
        :rule="{
          required: true,
          message: '请选择权限菜单',
        }">
          <n-tree key-field="id" label-field="menuName" cascade :selectable="false" :data="permissionTree"
            :checked-keys="modalForm.menuIds" :on-update:checked-keys="(keys) => (modalForm.menuIds = keys)"
            default-expand-all :default-checked-keys="modalForm.menuIds" checkable check-on-click
            class="cus-scroll max-h-200 w-full" :disabled="modalAction == 'view'" />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { NButton, NSwitch } from 'naive-ui'
import api from './api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"

defineOptions({ name: 'RoleMgt' })

const router = useRouter()

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

const { modalRef, modalFormRef, modalAction, modalForm, handleAdd, handleDelete, handleEdit, handleView }
  = useCrud({
    name: '角色',
    doCreate: api.create,
    doDelete: api.delete,
    doUpdate: api.update,
    initForm: { status: '0' },
    refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
  })

const columns = [
  // { title: 'id', key: 'id', width: 180, ellipsis: { tooltip: true } },
  { title: '角色名',width: 200, key: 'roleName' },
  { title: '是否内置',width: 200, key: 'builtIn', render: row => h('span', row.builtIn === '0' ? '是' : '否') },
  {
    title: '状态',
    key: 'status',
    render: row =>
      h(
        NSwitch,
        {
          size: 'small',
          rubberBand: false,
          value: row.status,
          loading: !!row.enableLoading,
          checkedValue: "0",
          uncheckedValue: "1",
          disabled: row.builtIn === '0',
          onUpdateValue: () => handleEnable(row),
        },
        {
          checked: () => '启用',
          unchecked: () => '停用',
        },
      ),
  },
  {
    title: '操作',
    key: 'actions',
    width: 220,
    align: 'right',
    fixed: 'right',
    render(row) {
      if (row.builtIn === '0') return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleView(row),
          },
          {
            default: () => '查看',
          },
        ),
      ]
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-left: 12px;',
            disabled: row.builtIn === '0',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),

        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            disabled: row.builtIn === '0',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]

async function handleEnable(row) {
  row.enableLoading = true
  const status = row.status === '0' ? '1' : '0'
  try {
    await api.update({ ...row, status })
    row.enableLoading = false
    $message.success('操作成功')
    $table.value?.handleSearch()
  }
  catch (error) {
    console.error(error)
    row.enableLoading = false
  }
}

const permissionTree = ref([])
api.getAllPermissionTree().then(({ data = [] }) => (permissionTree.value = data))
</script>
