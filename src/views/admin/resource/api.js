import { request } from '@/utils'
import axios from 'axios'
import { menuData } from './mock'


export default {
  getMenuTree: (data = {}) =>
    request.post('/v2/system/menu/list', data)
  ,
  getButtons: ({ parentId }) => request.get(`/v2/system/menu/add`),
  getComponents: () => axios.get(`${import.meta.env.VITE_PUBLIC_PATH}components.json`),
  addPermission: data => request.post('/v2/system/menu/add', data),
  updatePermission: data => request.post('/v2/system/menu/update', data),
  queryPermissions: data => request.post('/v2/system/menu/list', data),
  // savePermission: (data) => request.patch(`/v2/system/menu/add`, data),
  deletePermission: menuId => request.get(`/v2/system/menu/delete`, { params: { menuId } }),

}
