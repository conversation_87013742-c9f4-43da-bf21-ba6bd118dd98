<template>
  <MeModal ref="modalRef">
    <n-form ref="modalFormRef" label-placement="left" require-mark-placement="left" :label-width="100"
      :model="modalForm">
      <n-grid :cols="24" :x-gap="24">
        <n-form-item-gi :span="12" label="所属菜单" path="parentId">
          <n-tree-select v-model:value="modalForm.parentId" :options="menuOptions" :disabled="parentIdDisabled"
            :render-prefix="renderPrefix" label-field="menuName" key-field="id" placeholder="根菜单" clearable />
        </n-form-item-gi>
        <!-- <n-form-item-gi :span="12" label="类型" path="menuType">
          <n-radio-group v-model:value="modalForm.menuType" name="radiogroup">
            <n-space>
              <n-radio v-for="item in menuTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi> -->
        <n-form-item-gi :span="12" path="menuName" :rule="required">
          <template #label>
            <QuestionLabel label="名称" content="标题" />
          </template>
          <n-input v-model:value="modalForm.menuName" />
        </n-form-item-gi>
        <n-form-item-gi label="编码" :span="12" path="code">
          <n-input v-model:value="modalForm.code" />
        </n-form-item-gi>
        <!-- 类型（M目录 C菜单 F按钮） -->

        <n-form-item-gi v-if="modalForm.menuType === 'C'" :span="12" path="icon">
          <template #label>
            <QuestionLabel label="菜单图标" content="如material-symbols:help，图标库地址: https://icones.js.org/collection/all" />
          </template>
          <n-select v-model:value="modalForm.icon" :options="iconOptions" clearable filterable />
        </n-form-item-gi>
        <n-form-item-gi v-if="modalForm.menuType === 'C'" :span="12" path="path" :rule="{
          trigger: ['blur', 'change'],
          type: 'string',
          message: '必须是/、http、https开头',
          // validator(rule, value) {
          //   if (value) {
          //     return /\/|http|https/.test(value)
          //   }
          //   return true
          // },
        }">
          <template #label>
            <QuestionLabel label="路由地址" content="父级菜单可不填" />
          </template>
          <n-input v-model:value="modalForm.path" />
        </n-form-item-gi>

        <n-form-item-gi v-if="modalForm.menuType === 'C'" :span="24" path="component">
          <template #label>
            <QuestionLabel label="组件路径" content="前端组件的路径，以 /src 开头，父级菜单可不填" />
          </template>
          <n-select v-model:value="modalForm.component" :options="componentOptions" clearable filterable tag />
        </n-form-item-gi>

        <!-- 是否为外链（0是 1否） -->
        <n-form-item-gi :span="12" path="isFrame">
          <template #label>
            <QuestionLabel label="是否外链" content="如果是菜单，禁用后将不添加到路由表，无法进入此页面" />
          </template>
          <n-switch v-model:value="modalForm.isFrame" checked-value="0" unchecked-value="1">
            <template #checked>
              是
            </template>
            <template #unchecked>
              否
            </template>
          </n-switch>
        </n-form-item-gi>
        <!-- 菜单状态（0正常 1停用） -->
        <n-form-item-gi :span="12" path="status">
          <template #label>
            <QuestionLabel label="状态" content="如果是菜单，禁用后将不添加到路由表，无法进入此页面" />
          </template>
          <n-switch v-model:value="modalForm.status" checked-value="0" unchecked-value="1">
            <template #checked>
              启用
            </template>
            <template #unchecked>
              禁用
            </template>
          </n-switch>
        </n-form-item-gi>
        <!-- 显示状态（0显示 1隐藏） -->
        <n-form-item-gi label="显示" :span="12" path="visible">
          <n-switch v-model:value="modalForm.visible" checked-value="0" unchecked-value="1">
            <template #checked>
              显示
            </template>
            <template #unchecked>
              隐藏
            </template>
          </n-switch>
        </n-form-item-gi>
        <!-- 是否缓存（0缓存 1不缓存） -->
        <n-form-item-gi v-if="modalForm.menuType === 'C'" :span="12" path="isCache">
          <template #label>
            <QuestionLabel label="KeepAlive" content="设置keepAlive需将组件的name设置成当前菜单的code" />
          </template>
          <n-switch v-model:value="modalForm.isCache" checked-value="0" unchecked-value="1">
            <template #checked>
              是
            </template>
            <template #unchecked>
              否
            </template>
          </n-switch>
        </n-form-item-gi>
        <n-form-item-gi v-if="modalForm.menuType === 'C'" :span="12" label="排序" path="orderNum" :rule="{
          type: 'number',
          required: true,
          message: '此为必填项',
          trigger: ['blur', 'change'],
        }">
          <n-input-number v-model:value="modalForm.orderNum" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </MeModal>
</template>

<script setup>
import { MeModal } from '@/components'
import { useForm, useModal } from '@/composables'
// import icons from 'isme:icons'
import pagePathes from 'isme:page-pathes'
import api from '../api'
import QuestionLabel from './QuestionLabel.vue'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import { icons } from '@/utils'

const props = defineProps({
  menus: {
    type: Array,
    required: true,
  },
})
const emit = defineEmits(['refresh'])

const menuOptions = computed(() => {
  return [{ menuName: '根菜单', id: '', icon: 'TablerFolderRoot', children: props.menus || [] }]
})
const componentOptions = pagePathes.map(path => ({ label: path, value: path }))
const iconOptions = icons.map(item => ({
  label: () =>
    h('span', { class: 'flex items-center gap-2' }, [h(SvgIcon, { icon: `${item}`, class: 'shrink-0' }), item]),
  value: item,
}))
const menuTypeOptions = [
  // { label: '目录', value: 'M' },
  { label: '菜单', value: 'C' },
  { label: '按钮', value: 'F' },
]
const required = {
  required: true,
  message: '此为必填项',
  trigger: ['blur', 'change'],
}

const defaultForm = { query: '', isFrame: '1', isCache: '0', visible: '0', orderNum: 1, menuType: 'C', parentId: '', }
const [modalFormRef, modalForm, validation] = useForm()
const [modalRef, okLoading] = useModal()

const modalAction = ref('')
const parentIdDisabled = ref(false)
function handleOpen(options = {}) {
  const { action, row = {}, ...rest } = options
  modalAction.value = action
  modalForm.value = { ...defaultForm, ...row }
  parentIdDisabled.value = !!row.parentId && row.type === 'BUTTON'
  modalRef.value.open({ ...rest, onOk: onSave })
}

async function onSave() {

  await validation()
  okLoading.value = true
  // DashiconsButton
  if (modalForm.value.menuType === 'F') modalForm.value.icon = 'fluent:button-20-regular'
  try {
    let newFormData
    if (!modalForm.value.parentId)
      modalForm.value.parentId = null
    if (modalAction.value === 'add') {
      const res = await api.addPermission(modalForm.value)
      newFormData = res.data
    }
    else if (modalAction.value === 'edit') {
      await api.updatePermission(modalForm.value)
    }
    okLoading.value = false
    $message.success('保存成功')
    emit('refresh', modalAction.value === 'add' ? newFormData : modalForm.value)
  }
  catch (error) {
    console.error(error)
    okLoading.value = false
    return false
  }
}
function renderPrefix({ option }) {
  return h(SvgIcon, { icon: `${option.icon}` })
}
defineExpose({
  handleOpen,
})
</script>
