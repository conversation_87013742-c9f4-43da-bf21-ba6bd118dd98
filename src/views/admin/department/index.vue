<template>
  <CommonPage>
    <template #action>
      <NButton type="primary" @click="handleAdd()">
        <SvgIcon icon="akar-icons:plus"></SvgIcon>
        创建部门
      </NButton>
    </template>

    <AppCard class="mb-[20px] pl-[12px] min-h-15 border rounded bg-gray-100	">
      <form class="flex justify-between p-1">
        <div class="flex gap-2">
          <MeQueryItem label="公司" :label-width="50">
            <n-select v-model:value="queryItems.companyId" :options="companys" label-field="name" value-field="id" />
          </MeQueryItem>
          <MeQueryItem label="部门名称" :label-width="70">
            <n-input v-model:value="queryItems.deptName" type="text" placeholder="请输入部门名" clearable />
          </MeQueryItem>
        </div>
        <div class="flex-shrink-0 p-2.5">
          <n-button class="ml-5" type="primary" @click="initDept">
            <SvgIcon icon="lucide:search"></SvgIcon>
            搜索
          </n-button>

        </div>
      </form>
    </AppCard>
    <NDataTable :scroll-x="true" :columns="columns" :row-key="(row) => row['id']" :data="deptList" />

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
        :disabled="modalAction === 'view'">
        <n-form-item label="所属公司" path="companyId" :rule="{
          required: true,
          message: '请选择角色',
          trigger: ['input', 'blur'],
        }">
          <n-select v-model:value="modalForm.companyId" clearable :options="companys" label-field="name"
            value-field="id" />
        </n-form-item>
        <n-form-item :span="12" label="所属部门" path="parentId">
          <n-tree-select v-model:value="modalForm.parentId" :options="deptData" label-field="deptName" key-field="id"
            placeholder="请选择" clearable />
        </n-form-item>
        <n-form-item label="部门名称" path="deptName" :rule="{
          required: true,
          message: '请输入部门名称',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.deptName" />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { treeTraverse, tree2list } from '@/utils'
import { NButton } from 'naive-ui'
import api from './api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"


defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})
const deptList = ref([])

onMounted(() => {
  initCompany()
})

const initDept = () => {
  api.read({
    companyId: queryItems.value.companyId,
    name: queryItems.value.deptName
  }).then(({ data = [] }) => {
    deptList.value = data
  })
}
const companys = ref([])
const initCompany = () => {
  api.getAllCompanys().then(({ data = [] }) => {
    companys.value = data
    queryItems.value.companyId = data[0].id
    initDept()
  })
}


const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd: originalHandleAdd,
  handleEdit: originalHandleEdit,
  handleDelete,
} = useCrud({
  name: '部门',
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: initDept,
})

// 重写 handleAdd 方法，在打开弹框时调用 initFormDept
const handleAdd = (row = {}, title) => {
  // 在弹框打开后调用 initFormDept 获取最新部门信息
  nextTick(() => {
    initFormDept()
  })
  originalHandleAdd(row, title)
}

// 重写 handleEdit 方法，在打开弹框时调用 initFormDept
const handleEdit = (row, title) => {
  // 在弹框打开后调用 initFormDept 获取最新部门信息
  nextTick(() => {
    initFormDept()
  })
  originalHandleEdit(row, title)
}


const columns = [
  // {
  //   type: 'selection',
  // },
  { title: '名称', key: 'deptName', width: 400, ellipsis: { tooltip: true } },
  {
    title: '公司名称', key: 'companyName', ellipsis: { tooltip: true }, render(row) {
      const company = companys.value.find(item => item.id === row.companyId)
      return company?.name
    }
  },

  // { title: '排序', key: 'orderNum', width: 150, ellipsis: { tooltip: true } },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h(SvgIcon, { icon: 'mi:delete' }),
          },
        ),
      ]
    },
  },
]


watch(() => queryItems.value.companyId, () => {
  initDept()
})

watch(() => modalForm.value.companyId, (newCompanyId, oldCompanyId) => {
  // 当公司发生变化时，清空上级部门选择
  if (newCompanyId !== oldCompanyId) {
    modalForm.value.parentId = null
  }
  // 重新获取新公司的部门列表
  initFormDept()
})
const deptData = ref([])

const initFormDept = () => {
  // 如果没有选择公司，清空部门选项
  if (!modalForm.value.companyId) {
    deptData.value = []
    return
  }

  api.read({
    companyId: modalForm.value.companyId
  }).then(({ data = [] }) => {
    deptData.value = data
  }).catch(() => {
    // 如果获取部门失败，也清空部门选项
    deptData.value = []
  })
}


</script>
