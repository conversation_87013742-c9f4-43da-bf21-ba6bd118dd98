<template>
  <MeModal ref="modalRef">
    <n-form ref="modalFormRef" label-placement="left" require-mark-placement="left" :label-width="100"
      :model="modalForm">
      <n-grid :cols="24" :x-gap="24">
        <n-form-item-gi :span="12" label="所属" path="parentId">
          <n-tree-select v-model:value="modalForm.parentId" :options="menuOptions" :disabled="true"
            :render-prefix="renderPrefix" label-field="name" key-field="id" placeholder="根菜单" clearable />
        </n-form-item-gi>
        <n-form-item-gi :span="12" label="名称" path="deptName" :rule="required">
          <n-input v-model:value="modalForm.deptName" />
        </n-form-item-gi>
        <!-- <n-form-item-gi label="编码" :span="12" path="code">
          <n-input v-model:value="modalForm.code" />
        </n-form-item-gi> -->
        <n-form-item-gi :span="12" label="排序" path="orderNum" :rule="{
          type: 'number',
          required: true,
          message: '此为必填项',
          trigger: ['blur', 'change'],
        }">
          <n-input-number v-model:value="modalForm.orderNum" />
        </n-form-item-gi>
        <!-- <n-form-item-gi label="状态" :span="12" path="status">
          <n-switch v-model:value="modalForm.status" checked-value="0" unchecked-value="1">
            <template #checked>
              启用
            </template>
<template #unchecked>
              禁用
            </template>
</n-switch>
</n-form-item-gi> -->
      </n-grid>
    </n-form>
  </MeModal>
</template>

<script setup>
import { MeModal } from '@/components'
import { useForm, useModal } from '@/composables'
import api from './api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"


const icons = ['ri:chat-ai-line', 'mdi:report-bar', 'carbon:document-requirements', 'hugeicons:workflow-square-03', 'ri:admin-line', 'carbon:location-company', 'mdi:company', 'fluent:organization-16-regular', 'solar:user-linear', 'carbon:user-role', 'fluent:navigation-32-regular', 'streamline:dictionary-language-book']

const props = defineProps({
  parentNode: {
    type: Object,
    default: () => { },
  },
  menus: {
    type: Array,
    required: true,
  },
})
const emit = defineEmits(['refresh'])

// const menuOptions = computed(() => {
//   return [{ deptName: props.parentNode?.name || '根菜单', id: '', icon: 'TablerFolderRoot', children: props.menus || [] }]
// })

const required = {
  required: true,
  message: '此为必填项',
  trigger: ['blur', 'change'],
}

const defaultForm = { status: '0' }
const [modalFormRef, modalForm, validation] = useForm()
const [modalRef, okLoading] = useModal()

const menuOptions = ref([])
const modalAction = ref('')
const parentIdDisabled = ref(false)
function handleOpen(options = {}) {
  const { action, row = {}, ...rest } = options
  const parentNode = row.parentNode
  const currentNode = row.currentNode
  const companyNode = row.companyNode
  modalAction.value = action
  // menuOptions.value = props.menus
  menuOptions.value = [parentNode].map(item => ({ name: item.name || item.deptName, id: item.id, icon: 'TablerFolderRoot', children: item.children || [] }))

  modalForm.value = { ...defaultForm, parentId: parentNode.id, ...currentNode, companyId: companyNode?.id || currentNode.id }
  delete modalForm.value.row
  delete modalForm.value.row
  delete modalForm.value.parentNode
  // parentIdDisabled.value = !!row.parentId && row.type === 'BUTTON'
  modalRef.value.open({ ...rest, onOk: onSave })
}

async function onSave() {

  await validation()
  // 公司下的一级部门没有 parentId
  if (modalForm.value.parentId === modalForm.value.companyId) {
    modalForm.value.parentId = null

  }
  okLoading.value = true
  try {
    let newFormData
    if (!modalForm.value.parentId)
      modalForm.value.parentId = null
    if (modalAction.value === 'add') {

      const res = await api.create(modalForm.value)
      newFormData = res.data
    }
    else if (modalAction.value === 'edit') {
      await api.update(modalForm.value)
    }
    okLoading.value = false
    $message.success('保存成功')
    emit('refresh', modalAction.value === 'add' ? newFormData : modalForm.value)
  }
  catch (error) {
    console.error(error)
    okLoading.value = false
    return false
  }
}
function renderPrefix({ option }) {
  return h(SvgIcon, { icon: `${option.icon}` })
}
defineExpose({
  handleOpen,
})
</script>
