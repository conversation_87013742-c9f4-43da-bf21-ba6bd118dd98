
import { request } from '@/utils'

export default {
  create: data => request.post('/v2/system/dept/add', data),
  readById: (params = {}) => request.get(`/v2/system/dept/getById/`, { params }),
  read: (data = {}) => request.post('/v2/system/dept/list', data),
  update: data => request.post(`/v2/system/dept/update`, data),
  delete: (deptId) => request.get(`/v2/system/dept/delete`, { params: { deptId } }),
  resetPwd: (id, data) => request.patch(`/user/password/reset/${id}`, data),

  getAllCompanys: (data = {}) => request.post('/v2/system/company/list', data),
}
