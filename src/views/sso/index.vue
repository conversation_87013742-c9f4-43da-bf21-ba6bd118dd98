<template>
    <div class="sso_page">
        <n-space>
            <n-spin size="large" />
        </n-space>
    </div>
</template>

<script setup>
import { useAuthStore } from '@/store'
import { lStorage, throttle } from '@/utils'
import { useStorage } from '@vueuse/core'
import api from './api'
import { onMounted } from 'vue'

const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()

onMounted(async () => {
    valiteIsLogin()
})


const valiteIsLogin = async () => {
    let queryInfo = route.query
    let res = await api.securityLogin(queryInfo)
    authStore.setToken(res.data)
    router.push({ path: queryInfo.page })
}

</script>

<style scoped>
.sso_page {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>