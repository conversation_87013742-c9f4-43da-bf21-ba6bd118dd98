<template>
  <div class="icon-test-page p-6">
    <h1 class="text-2xl font-bold mb-6">图标测试页面</h1>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">缓存统计</h2>
      <div class="bg-gray-100 p-4 rounded">
        <p>总图标数: {{ cacheStats.totalIcons }}</p>
        <p>已缓存图标数: {{ cacheStats.cachedIcons }}</p>
        <p>初始化状态: {{ cacheStats.isInitialized ? '已完成' : '未完成' }}</p>
      </div>
    </div>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">常用图标测试</h2>
      <div class="grid grid-cols-6 gap-4">
        <div 
          v-for="iconName in testIcons" 
          :key="iconName"
          class="flex flex-col items-center p-4 border rounded hover:bg-gray-50"
        >
          <SvgIcon :icon="iconName" class="text-2xl mb-2" />
          <span class="text-xs text-center">{{ iconName }}</span>
        </div>
      </div>
    </div>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">动态测试</h2>
      <div class="flex items-center gap-4 mb-4">
        <input 
          v-model="customIcon" 
          placeholder="输入图标名称，如: ri:chat-ai-line"
          class="border px-3 py-2 rounded flex-1"
        />
        <button 
          @click="refreshStats"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          刷新统计
        </button>
      </div>
      <div v-if="customIcon" class="flex items-center gap-4">
        <SvgIcon :icon="customIcon" class="text-3xl" />
        <span>{{ customIcon }}</span>
      </div>
    </div>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">所有可用图标</h2>
      <div class="grid grid-cols-8 gap-2 max-h-96 overflow-y-auto">
        <div 
          v-for="iconName in allIcons" 
          :key="iconName"
          class="flex flex-col items-center p-2 border rounded hover:bg-gray-50 cursor-pointer"
          @click="customIcon = iconName"
        >
          <SvgIcon :icon="iconName" class="text-lg mb-1" />
          <span class="text-xs text-center truncate w-full" :title="iconName">
            {{ iconName.split(':')[1] }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { getCacheStats } from '@/utils/iconLoader';
import iconMap from '@/assets/icons/icon-map.json';

const cacheStats = ref({
  totalIcons: 0,
  cachedIcons: 0,
  isInitialized: false
});

const customIcon = ref('ri:chat-ai-line');

const testIcons = [
  'ri:chat-ai-line',
  'mdi:report-bar',
  'carbon:document-requirements',
  'hugeicons:workflow-square-03',
  'ri:admin-line',
  'akar-icons:plus',
  'lucide:search',
  'mingcute:edit-line',
  'mi:delete',
  'mynaui:star-solid',
  'material-symbols:search',
  'mdi:logout'
];

const allIcons = Object.keys(iconMap);

const refreshStats = () => {
  cacheStats.value = getCacheStats();
};

onMounted(() => {
  refreshStats();
  
  // 每秒刷新一次统计，直到初始化完成
  const interval = setInterval(() => {
    refreshStats();
    if (cacheStats.value.isInitialized) {
      clearInterval(interval);
    }
  }, 1000);
});
</script>

<style scoped>
.icon-test-page {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
