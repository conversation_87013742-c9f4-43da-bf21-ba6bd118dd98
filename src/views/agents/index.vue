<template>
  <n-layout class="h-full w-full" has-sider>
    <n-layout-sider class="h-full w-full" bordered show-trigger collapse-mode="width" :collapsed-width="64" :width="240"
      :native-scrollbar="true" :inverted="inverted" :on-update:collapsed="updateCollapsed">
      <div class="flex flex-col justify-between h-full pb-1">
        <n-scrollbar class="h-full">
          <Menu :collapsed="collapsed" :data="agentStore.groupList"></Menu>
        </n-scrollbar>
        <Menu :collapsed="collapsed" :data="agentStore.footerMenus"></Menu>
        <!-- <n-menu v-if="!appStore.getIsPreview" v-model:value="sideFooterValue" :inverted="inverted" :collapsed-width="64"
          :collapsed-icon-size="22" :options="sideFooterMenus" label-field="name" key-field="id" class="text-[#26334b]"
          :style="{
            '--n-item-text-color': '#26334b !important'
          }" :on-update:value="groupChange" /> -->
      </div>
    </n-layout-sider>

    <n-layout-content class="agents-container h-full w-full">
      <iframe v-if="agentStore.currentAgent?.url" :src="agentStore.currentAgent?.url" frameborder="0" width="100%"
        height="95%"></iframe>
      <div v-else>
        <div class="w-full text-center text-white mt-6">
          <p class="text-3xl font-bold mb-2">智能体大厅</p>
          <!-- <p class="text-sm">汇聚上百种AI写作蓝本，激发创意之火，AI让工作效率飞跃升级</p> -->
        </div>
        <div class="search w-[60%] mx-auto mt-6 ">
          <n-input class="h-[52px] rounded-xl	leading-[52px]" v-model:value="search" placeholder="请输入要搜索的内容">
            <!-- <template #suffix>
              <n-button size="" type="primary" class="rounded-lg">搜索</n-button>
            </template> -->
          </n-input>
        </div>

        <div class="agents-list grid grid-rows-3 grid-wrap grid-cols-4	gap-4 mt-6 px-5">
          <!-- <div class="agents-list grid grid-rows-3 	auto-cols-min	gap-2 mt-10 px-5"> -->
          <!-- <div class="agents-list flex flex-wrap	gap-2 mt-10 px-5"> -->
          <Item 
            v-for="(item, index) in agentList" 
            :item="item" 
            :key="index"
            @click="agentViews(item)">
          </Item>
        </div>
      </div>
    </n-layout-content>
  </n-layout>
</template>
<script setup>
import { ref, computed } from 'vue'
import { useAgents } from './useAgents.js'
import Item from "./Item.vue"
import Menu from '@/components/menu/index.vue'
import { useAgentStore, useAppStore } from "@/store";
import api from '@/api'

const agentStore = useAgentStore()
const appStore = useAppStore()

const { search, agentList, sideFooterMenus, collapsed, sideFooterValue,
  updateCollapsed, groupChange } = useAgents()

const agentViews = async (agentInfo) => {
  const { data } = await api.getStatisticTimesApi(agentInfo.id)
}

</script>
<style lang="scss" scoped>
.agents-container {
  width: 100%;
  height: 100%;
  background: url('@/assets/agent-bg.png') center top / 1920px no-repeat rgb(249, 253, 255);
  background-size: 100%;
  box-sizing: border-box;
  flex-direction: column;
  position: relative;
}

@media (min-width: 750px) {
  .fsl\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1024px) {
  .lg\:block {
    display: block !important
  }

  .lg\:flex {
    display: flex !important
  }

  .lg\:px-8 {
    padding-left: 2rem !important;
    padding-right: 2rem !important
  }
}

@media (min-width: 1250px) {
  .fxl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important
  }

  .fxl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1280px) {
  .xl\:flex {
    display: flex !important
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1680px) {
  .fxl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important
  }

  .fxl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important
  }
}

@media (min-width: 1960px) {
  .fxl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important
  }
}
</style>