<template>
  <div class="agent-item min-h-[110px] border rounded-lg p-4 pr-7 bg-white cursor-pointer " @click="toAgent">
    <div class="star-icon " @click.stop="handleStar">
      <SvgIcon icon="mynaui:star-solid" :width="20" :height="20" :color="item?.isStar ? '#FFD700' : '#ccc'" />
    </div>
    <div class="flex items-center gap-2">
      <n-avatar class="shrink-0" :src="item.icon" :renderFallback="renderFallback(item.name)" round />
      <span class="font-bold text-sm">{{ item.name }}</span>
    </div>
    <p class="text-xs text-[#666] mt-2">{{ item.description || item.name }}</p>
  </div>
</template>
<script setup>
import { useAgentStore } from "@/store/modules/agent";
import api from "@/api";
const agentStore = useAgentStore()
const props = defineProps({
  item: {
    type: Object,
    default: () => { },
  },
})

const renderFallback = (name) => {
  return () => h('div', { class: "flex shrink-0 justify-center items-center justify-center w-full h-full text-xs text-[#ffffff] rounded-full bg-[#749BFF]" }, name.slice(0, 2))
}
const handleStar = () => {
  console.log('收藏')
  // 收藏 取消收藏
  if (props.item.isStar) {
    api.cancelCollectionAgent(props.item.id)
  } else {
    api.collectionAgent(props.item.id)
  }
  props.item.isStar = !props.item.isStar
}
// 跳转 dify 问答页面 import.meta.env.VITE_DIFY_URL 
const toAgent = () => {
  agentStore.setCurrentAgent(props.item)
  // TODO 后续需要区分不同来源，dify fastgpt
  // const url = import.meta.env.VITE_DIFY_URL + '/chat/' + props.item.code
  // window.open(props.item.url)
}
</script>
<style lang="scss" scoped>
.agent-item {
  position: relative;
  transition: all 0.2s ease-in-out;

  &:hover {
    border: 1px solid var(--dify_color_primary);
    transform: scale(1.04);
  }

  .star-icon {
    position: absolute;
    right: 5px;
    top: 5px;
  }
}
</style>