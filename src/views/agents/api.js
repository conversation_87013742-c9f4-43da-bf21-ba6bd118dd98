
import { request } from '@/utils'

export default {
  // /v2/system/apps/list
  getAllApps: () => request.get('/v2/system/agent/hallList?companyId=39c99e73-8479-4fbf-adc7-bd32aa4f3d32'),

  getMenuTree: (userId) => request.get('/v2/system/menu/menuTree', { params: { userId } }),
  getMenuList: (data = {}) => request.post('/v2/system/menu/list', data),
  getAllCompanys: (data = {}) => request.post('/v2/system/company/list', data),
  linkList: (data = {}) => request.post('/v2/system/link/list', data),

  groupList: (data = {}) => request.post('/v2/system/group/list', data),



}
