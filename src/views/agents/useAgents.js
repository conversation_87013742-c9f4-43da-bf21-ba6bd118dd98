import { ref, onMounted, watch, h, computed } from "vue"
import { useAppStore } from "@/store/modules/app";
import { useAgentStore } from "@/store/modules/agent";
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import { treeTraverse } from "@/utils/tools";


export const useAgents = () => {
  const sideFooterMenus = ref([
    {
      name: '我的收藏',
      key: 'favorites',
      iconName: 'lets-icons:favorites-duotone'
    }
  ])

  const appStore = useAppStore()
  const agentStore = useAgentStore()
  const search = ref('')
  const agentList = computed(() => agentStore.agentList.filter(item => item.name.includes(search.value)))
  onMounted(async () => {
    // 预览不需要初始化
    !appStore.getIsPreview && agentStore.initGroupList()
  })


  function renderIcon(icon) {
    // 增加 icon 属性
    return () => h(SvgIcon, { icon })
  }

  // 收藏切换
  const sideFooterValue = ref("")
  const groupChange = (_, item) => {
    treeTraverse(agentStore.groupList, (item) => {
      item.active = false;
    });
    agentStore.initAgentList(item)

  }

  const collapsed = ref(false)
  const updateCollapsed = () => {
    collapsed.value = !collapsed.value

  }
  return {
    search,
    sideFooterMenus,
    agentList,
    collapsed,
    updateCollapsed,
    sideFooterValue,
    groupChange,
  }
}