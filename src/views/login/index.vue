<template>
  <div class="w-full h-full flex flex-col bg-[url(@/assets/images/login_bg.webp)] bg-cover">
    <div class="m-auto max-w-[700px] min-w-[345px] flex justify-center items-center shadow-lg rounded-8 bg-opacity-20 bg-cover p-3 auto-bg">
      <div class="hidden w-[380px] px-5 py-9 md:block">
        <img src="@/assets/images/login_banner.webp" class="w-full" alt="login_banner">
      </div>

      <div class="w-[320px] flex-col px-4 py-8">
        <h2 class="flex justify-center items-center text-2xl text-[#6a6a6a] font-normal">
          <img src="@/assets/images/logo.png" class="mr-3 h-[50px]">
          <!-- {{ title }} -->
        </h2>
        <n-input v-model:value="loginInfo.username" autofocus class="mt-8 h-10 items-center" placeholder="请输入用户名"
          :maxlength="20">
          <template #prefix>
            <SvgIcon icon="tabler:user-filled" class="mr-3 opacity-20" />
          </template>
        </n-input>
        <n-input v-model:value="loginInfo.password" class="mt-5 h-10 items-center" type="password"
          show-password-on="mousedown" placeholder="请输入密码" :maxlength="20" @keydown.enter="handleLogin()">
          <template #prefix>
            <SvgIcon icon="bxs:lock" class="mr-3 opacity-20" />
          </template>
        </n-input>

        <div class="mt-8 flex items-center">
          <n-input v-model:value="loginInfo.code" class="h-10 items-center" palceholder="请输入验证码" :maxlength="4"
            @keydown.enter="handleLogin()">
            <template #prefix>
              <SvgIcon icon="material-symbols-light:key-rounded" class="mr-3 opacity-20" />
            </template>
          </n-input>
          <img 
            v-if="captchaUrl" 
            :src="'data:image/png;base64,'+captchaUrl" 
            alt="验证码" 
            height="40" 
            class="ml-3 w-20 cursor-pointer"
            @click="initCaptcha">
        </div>

        <n-checkbox class="mt-5" :checked="isRemember" label="记住我" :on-update:checked="(val) => (isRemember = val)" />

        <div class="mt-5 flex items-center">
          <!-- <n-button class="h-10 flex-1 rounded-5 text-base" type="primary" ghost @click="quickLogin()">
            一键体验
          </n-button> -->

          <n-button class="h-10 flex-1 rounded-5 text-base" type="primary" :loading="loading" @click="handleLogin()">
            登录
          </n-button>
        </div>
      </div>
    </div>

    <TheFooter class="py-3" />
  </div>
</template>

<script setup>
import { useAuthStore } from '@/store'
import { lStorage, throttle } from '@/utils'
import { useStorage } from '@vueuse/core'
import api from './api'

const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()
const title = '行业解决方案智能体平台' //import.meta.env.VITE_TITLE

const loginInfo = ref({
  username: '',
  password: '',
  code: '',
})

const captchaUrl = ref('')
let uuid = ref('')
const initCaptcha = throttle(async () => {
   let { data } = await api.getCode()
   captchaUrl.value = data.img
   uuid.value = data.uuid
}, 500)

const localLoginInfo = lStorage.get('loginInfo')
if (localLoginInfo) {
  loginInfo.value.username = localLoginInfo.username || ''
  loginInfo.value.password = localLoginInfo.password || ''
}
initCaptcha()

// function quickLogin() {
//   loginInfo.value.username = 'admin'
//   loginInfo.value.password = '123456'
//   handleLogin(true)
// }

const isRemember = useStorage('isRemember', true)
const loading = ref(false)
async function handleLogin(isQuick) {
  const { username, password, code } = loginInfo.value
  if (!username || !password || !code)
    return $message.warning('请输入用户名和密码')
  if (!isQuick && !code) return $message.warning('请输入验证码')
  try {
    loading.value = true
    $message.loading('正在验证，请稍后...', { key: 'login' })
    const { data } = await api.login({ username, password: password.toString(), code, uuid: uuid.value })
    if (isRemember.value) {
      lStorage.set('loginInfo', { username, password, uuid: uuid.value})
    }
    else {
      lStorage.remove('loginInfo')
    }
    onLoginSuccess(data)
  }
  catch (error) {
    // 10003为验证码错误专属业务码
    if (error?.code === 10003) {
      // 为防止爆破，验证码错误则刷新验证码
      initCaptcha()
    }
    initCaptcha()
    $message.destroy('login')
    console.error(error)
  }
  loading.value = false
}

async function onLoginSuccess(data = {}) {
  authStore.setToken(data)
  // lStorage.set('tokenInfo', data)
  $message.loading('登录中...', { key: 'login' })
  try {
    $message.success('登录成功', { key: 'login' })
    if (route.query.redirect) {
      const path = route.query.redirect
      delete route.query.redirect
      router.push({ path, query: route.query })
    }
    else {
      router.push('/')
    }
  }
  catch (error) {
    console.error(error)
    $message.destroy('login')
  }
}
</script>
