<template>
  <CommonPage>
    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="需求名称" :label-width="60">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入用户名" clearable />
      </MeQueryItem>

      <MeQueryItem label="状态" :label-width="50">
        <n-select v-model:value="queryItems.status" clearable :options="[
          { label: '审核中', value: '1' },
          { label: '通过', value: '2' },
          { label: '驳回', value: '3' },
        ]" />
      </MeQueryItem>
    </MeCrud>

    <!-- 驳回 -->
    <n-modal v-model:show="rejectShow" preset="dialog" type="warning" title="驳回操作" positiveText="确定" negativeText="取消"
      @positive-click="handleReject" @negative-click="rejectShow = false">
      <n-form ref="rejectModalFormRef" label-placement="left" label-align="left" :label-width="80"
        :model="rejectModalForm">
        <n-form-item label="驳回理由" path="reason" :rule="{
          required: true,
          message: '请输入驳回理由',
          trigger: ['input', 'blur'],
        }">
          <n-input type="textarea" v-model:value="rejectModalForm.reason" />
        </n-form-item>
      </n-form>
    </n-modal>
    <DetailModal ref="detailModalRef"></DetailModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NAvatar, NButton, NPopover, NTag } from 'naive-ui'
import api from './api'
import $api from '@/api'
import { ref, reactive } from 'vue'
import { demandStatusOptions, mapDirtionaryKey, mapDirtionaryLabel, demandStatusMap } from '@/options/demand.js'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import DetailModal from '../DetailModal.vue'

defineOptions({ name: 'UserMgt' })

const $table = ref(null)
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
  getDictionaryList()
})

// 获取字典列表
let dictionaryListOptions = ref([])
const getDictionaryList = async () => {
  let { data } = await api.dictionaryList({
    type: 'demandType'
  })
  dictionaryListOptions.value = data
}

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleOpen,
} = useCrud({
  name: '需求',
  initForm: { status: true },
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: () => $table.value?.handleSearch(),
})

// 查看详情按钮
const detailsBtn = (row) => h(
  h(
    NButton,
    {
      size: 'small',
      type: 'default',
      style: 'margin-left: 12px;',
      onClick: () => details(row),
    },
    {
      default: () => '详情',
      icon: () => h(SvgIcon, { icon: 'ix:details' }),
    },
  ),
)
// 通过按钮
const passBtn = (row) => h(
  h(
    NButton,
    {
      size: 'small',
      type: 'primary',
      style: 'margin-left: 12px;',
      onClick: () => pass(row),
    },
    {
      default: () => '通过',
      icon: () => h(SvgIcon, { icon: 'iconoir:password-check' }),
    },
  ),
)

// 驳回按钮
const rejectBtn = (row) => h(
  h(
    NButton,
    {
      size: 'small',
      type: 'error',
      style: 'margin-left: 12px;',
      onClick: () => reject(row),
    },
    {
      default: () => '驳回',
      icon: () => h(SvgIcon, { icon: 'fluent:text-change-reject-20-filled' }),
    },
  ),
)

const columns = [
  { title: '公司名', key: 'companyName', ellipsis: { tooltip: true } },
  { title: '需求名称', key: 'name', ellipsis: { tooltip: true } },
  { title: '描述', key: 'description', ellipsis: { tooltip: true } },
  { title: '理由', key: 'reason', ellipsis: { tooltip: true } },
  {
    title: '需求类型',
    key: 'groupType',
    render(row) {
      return h('span', mapDirtionaryKey(row.groupType, dictionaryListOptions.value))
    },
  },
  {
    title: '状态',
    key: 'status',
    render: (row) => {
      return [
        row.status == 3 ?
          h(
            NPopover,
            { placement: 'top' },
            {
              trigger: () => h(
                NTag,
                { type: demandStatusMap[row.status] },
                { default: () => mapDirtionaryLabel(row.status, demandStatusOptions) },
              ),
              default: () => h('span', {}, { default: () => row.reason, })
            },
          ) :
          h(
            NTag,
            { type: demandStatusMap[row.status] },
            { default: () => mapDirtionaryLabel(row.status, demandStatusOptions) },
          )]
    },
  },
  { title: '创建人', key: 'createName', ellipsis: { tooltip: true } },
  { title: '创建时间', key: 'createAt', ellipsis: { tooltip: true } },
  {
    title: '操作',
    width: 280,
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      if (row.status === '1') {
        return [
          detailsBtn(row),
          passBtn(row),
          rejectBtn(row),
        ]
      } else {
        return [
          detailsBtn(row),
          // passBtn(row),
          // rejectBtn(row),
        ]
      }
    },
  },
]

const pass = (row) => {
  $dialog.warning({
    title: "温馨提示",
    content: `确定通过”${row.name}“的需求吗？`,
    positiveText: "确定",
    negativeText: "取消",
    draggable: true,
    onPositiveClick: async () => {
      await api.updateStatus({ demandId: row.id, status: '2' })
      $message.success("驳回成功");
      $table.value?.handleSearch()
      close()
    },
    onNegativeClick: () => {
      close()
    }
  });
}

let rejectShow = ref(false)
let rejectModalFormRef = ref(null)
let rejectModalForm = reactive({
  demandId: '',
  reason: '',
  status: '3',
})

const reject = (row) => {
  rejectModalForm.demandId = row.id
  rejectShow.value = true
}

const handleReject = async () => {
  await api.updateStatus(rejectModalForm)
  $message.success("驳回成功");
  $table.value?.handleSearch()
}

const detailModalRef = ref()
const details = (row) => {
  detailModalRef.value?.show(row)
}

</script>
<style lang="scss" scoped>
.formfile_box {
  // display: flex;
  // width: 50%;
  // flex-direction: column;

  .formfile_item {
    width: 100%;
    display: flex;
    justify-content: space-between;

    span {
      width: calc(100% - 20px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>