import { request } from '@/utils'

export default {
  create: data => request.post('/v2/system/demand/add', data),
  read: (data = {}) => request.post('/v2/system/demand/list', data),
  update: data => request.post(`/v2/system/demand/update`, data),
  delete: id => request.get(`/v2/system/demand/delete?demandId=${id}`),
  updateStatus: data => request.post(`/v2/system/demand/updateStatus`, data),
  dictionaryList: (data = {}) => request.post('/v2/system/dictionary/list', data),
}
