<template>
  <!-- 查看详情 -->
  <n-modal v-model:show="modalShow" style="width: 800px" preset="dialog" title="查看详情" :show-icon="false">
    <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
      :disabled="true">
      <n-form-item label="公司名称" path="name">
        {{ modalForm.companyName }}
      </n-form-item>
      <n-form-item label="需求名称" path="name">
        {{ modalForm.name }}
      </n-form-item>
      <n-form-item label="需求描述" path="description">
        {{ modalForm.description }}
      </n-form-item>
      <n-form-item label="需求类别" path="groupType">
        {{ mapDirtionaryKey(modalForm.groupType, dictionaryListOptions) }}
      </n-form-item>
      <n-form-item label="状态" path="status">
        {{ mapDirtionaryLabel(modalForm.status, demandStatusOptions) }}
      </n-form-item>
      <n-form-item v-if="modalForm.status == 3" label="驳回理由">
        {{ modalForm.reason }}
      </n-form-item>
      <n-form-item label="创建人" path="createName">
        {{ modalForm.createName }}
      </n-form-item>
      <n-form-item label="创建时间" path="createBy">
        {{ modalForm.createAt }}
      </n-form-item>
      <n-form-item label="修改人">
        {{ modalForm.updateName }}
      </n-form-item>
      <n-form-item label="修改时间" path="updateAt">
        {{ modalForm.updateAt }}
      </n-form-item>
      <n-form-item label="文件">
        <div class="w-[50%] flex flex-col gap-2">
          <div
            class="file-item flex items-center justify-between bg-gray-100 p-1 rounded-lg hover:bg-[#ECF1FF] cursor-pointer"
            v-for="(item, index) in modalForm.files" :key="index" @click="downloadFile(item)">
            <div class="flex gap-2 items-center">
              <SvgIcon icon="codex:file"></SvgIcon>
              <span class="truncate max-w-[280px]">{{ item.filename }}</span>
            </div>
            <!-- hover 展示 -->
            <div class="flex items-center gap-2">
              <SvgIcon icon="material-symbols:download" width="18" height="18"></SvgIcon>
            </div>
          </div>
        </div>
      </n-form-item>
    </n-form>
  </n-modal>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
import api from '@/api'
import { useRouter } from 'vue-router'
import { demandStatusOptions, mapDirtionaryKey, mapDirtionaryLabel, demandStatusMap } from '@/options/demand.js'

const router = useRouter()
const modalShow = ref(false)
const modalForm = ref({
})

onMounted(() => {
  getDictionaryList()
})
// 获取字典列表
let dictionaryListOptions = ref([])
const getDictionaryList = async () => {
  let { data } = await api.dictionaryList({
    type: 'DEMAND'
  })
  dictionaryListOptions.value = data
}

const downloadFile = async (file) => {
  window.open(file.url, '_blank')
}

const close = () => {
  modalShow.value = false
}

const show = (data) => {
  // await getDictionaryList()
  modalForm.value = data
  modalShow.value = true
}


const modalFormRef = ref(null)

defineExpose({
  show,
  close,
})
</script>
<style lang="scss" scoped></style>