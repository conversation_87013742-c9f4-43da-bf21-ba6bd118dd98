<template>
  <CommonPage>
    <template #action>
      <n-button type="primary" @click="onOpen">
        <SvgIcon icon="akar-icons:plus"></SvgIcon>
        新增需求
      </n-button>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="需求名称" :label-width="60">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入用户名" clearable />
      </MeQueryItem>

      <MeQueryItem label="状态" :label-width="50">
        <n-select v-model:value="queryItems.status" clearable :options="demandStatusOptions" />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px" :onOk="handleSave" okText="保存">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
        :disabled="modalAction === 'view'">
        <n-form-item label="需求名称" path="name" :rule="{
          required: true,
          message: '请输入需求名称',
          trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.name" />
        </n-form-item>
        <n-form-item label="需求描述" path="description" :rule="{
          required: true,
          message: '请输入需求描述',
          trigger: ['input', 'blur'],
        }">
          <n-input type="textarea" v-model:value="modalForm.description" />
        </n-form-item>
        <n-form-item label="需求类别" path="groupType" :rule="{
          required: true,
          message: '请选择需求类别',
          trigger: ['input', 'blur'],
        }">
          <n-select v-model:value="modalForm.groupType" clearable :options="dictionaryListOptions" label-field="value"
            value-field="key" />
        </n-form-item>
        <n-form-item label="文件上传">
          <div class="flex flex-col">
            <div class="file-list flex flex-col gap-2">
              <div class="file-item flex items-center justify-between bg-gray-100 p-1 rounded-lg hover:bg-[#ECF1FF]"
                v-for="(item, index) in modalForm.files" :key="index">
                <div class="flex gap-2 items-center">
                  <SvgIcon icon="codex:file"></SvgIcon>
                  <span>{{ item.filename }}</span>
                </div>
                <!-- hover 展示 -->
                <div class="flex items-center gap-2">
                  <SvgIcon icon="material-symbols:download" @click="handleFileAction('download', index)"></SvgIcon>
                  <SvgIcon icon="mi:delete" @click="handleFileAction('remove', index)"></SvgIcon>
                </div>
              </div>
              <FileUpload :max-size="20" @on-success="handleUploadSuccess" :defaultFileList="modalForm.files">
              </FileUpload>
            </div>
          </div>
        </n-form-item>
      </n-form>
    </MeModal>
    <DetailModal ref="detailModalRef"></DetailModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { NAvatar, NButton, NSwitch, NTag, NPopover, NPopconfirm } from 'naive-ui'
import api from './api'
import { ref } from 'vue'
import { demandStatusOptions, demandStatusMap, mapDirtionaryLabel, mapDirtionaryKey } from '@/options/demand.js'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import DetailModal from '../DetailModal.vue'

defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
  // lStorage.get('logininfo').uuid
  getDictionaryList()
})

// 获取字典列表
let dictionaryListOptions = ref([])
const getDictionaryList = async () => {
  let { data } = await api.dictionaryList({
    type: 'demandType'
  })
  dictionaryListOptions.value = data
}

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleOpen,
  handleDelete,
} = useCrud({
  name: '需求',
  initForm: {
    status: true
  },
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: () => $table.value?.handleSearch(),
})

// 编辑按钮
const editBtn = (row) => h(
  NButton,
  {
    size: 'small',
    type: 'primary',
    style: 'margin-left: 12px;',
    onClick: () => onEdit(row),
  },
  {
    default: () => '编辑',
    icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
  },
)

// 发布按钮
const publicBtn = (row) => h(
  NButton,
  {
    size: 'small',
    type: 'primary',
    style: 'margin-left: 12px;',
    onClick: () => onPublic(row),
  },
  {
    default: () => '发布',
    icon: () => h(SvgIcon, { icon: 'material-symbols:publish' }),
  },
)

// 删除按钮
const deleteBtn = (row) => h(
  h(
    NButton,
    {
      size: 'small',
      type: 'error',
      style: 'margin-left: 12px;',
      onClick: () => handleDelete(row.id),
    },
    {
      default: () => '删除',
      icon: () => h(SvgIcon, { icon: 'mi:delete' }),
    },
  ),
)

const columns = [
  { title: '需求名称', key: 'name', ellipsis: { tooltip: true } },
  { title: '描述', key: 'description', ellipsis: { tooltip: true } },
  {
    title: '需求类型',
    key: 'groupType',
    render(row) {
      return h('span', mapDirtionaryKey(row.groupType, dictionaryListOptions.value))
    },
  },
  {
    title: '状态',
    key: 'status',
    render: (row) => {
      return [
        row.status == 3 ?
          h(
            NPopover,
            { placement: 'top' },
            {
              trigger: () => h(
                NTag,
                { type: demandStatusMap[row.status] },
                { default: () => mapDirtionaryLabel(row.status, demandStatusOptions) },
              ),
              default: () => h('span', {}, { default: () => row.reason, })
            },
          ) :
          h(
            NTag,
            { type: demandStatusMap[row.status] },
            { default: () => mapDirtionaryLabel(row.status, demandStatusOptions) },
          )]
    },
  },
  { title: '创建人', key: 'createBy', ellipsis: { tooltip: true } },
  { title: '创建时间', key: 'createAt', ellipsis: { tooltip: true } },
  {
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    width: 280,
    hideInExcel: true,
    render(row) {
      let actions = []
      if (row.status === '0') {
        actions = [
          editBtn(row),
          publicBtn(row),
          deleteBtn(row),
        ]
      } else if (row.status === '3') {
        actions = [
          deleteBtn(row),
        ]
      }
      actions.push(detailsBtn(row))
      return actions
    },
  },
]

const detailModalRef = ref()
const details = (row) => {
  detailModalRef.value?.show(row)
}
// 查看详情按钮
const detailsBtn = (row) => h(
  h(
    NButton,
    {
      size: 'small',
      type: 'default',
      style: 'margin-left: 12px;',
      onClick: () => details(row),
    },
    {
      default: () => '详情',
      icon: () => h(SvgIcon, { icon: 'ix:details' }),
    },
  ),
)
const onOpen = () => {
  handleOpen({
    action: 'add',
    title: '创建需求',
    row: {
      files: [
      ]
    },
    onOk: handleSave,
  })
}

const onEdit = (row) => {
  modalForm.value = row.files.map(item => {
    item.name = item.filename
    return item
  })
  handleOpen({
    action: 'eidt',
    title: '编辑需求',
    row: row,
    onOk: handleEditSave,
  })
}

const onPublic = async (row) => {
  $dialog.warning({
    title: "温馨提示",
    content: `确定发布”${row.name}“的需求吗？`,
    positiveText: "确定",
    negativeText: "取消",
    draggable: true,
    onPositiveClick: () => {
      let data = Object.assign({}, row, { status: '1' })
      api.update(data).then(() => {
        $table.value?.handleSearch()
        modalRef.value?.close()
      })
    },
    onNegativeClick: () => {
      close()
    }
  });
}


const handleEditSave = async () => {
  await modalFormRef.value.validate()
  api.update(modalForm.value).then(() => {
    $table.value?.handleSearch()
    modalRef.value?.close()
  })
}

const handleSave = async () => {
  await modalFormRef.value.validate()
  modalForm.value.status = '0'
  api.create(modalForm.value).then(() => {
    $table.value?.handleSearch()
    modalRef.value?.close()
  })
}

const handleUploadSuccess = (data) => {
  modalForm.value.files = modalForm.value.files || []
  modalForm.value.files.push(data)
}

// 文件操作
const handleFileAction = (action, index) => {
  if (action === 'remove') {
    modalForm.value.files.splice(index, 1)
  } else if (action === 'download') {
    let file = modalForm.value.files[index]
    window.open(file.url)
  }
}
</script>