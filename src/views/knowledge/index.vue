<template>
  <CommonPage>
    <template #action>
      <n-space align="center">
        <n-input v-model:value="searchText" type="text" placeholder="搜索知识库" clearable style="width: 200px">
          <template #prefix>
            <SvgIcon icon="material-symbols:search" />
          </template>
        </n-input>
        <n-select v-model:value="filterType" :options="scopeTypeOptions" placeholder="权限类型" clearable style="width: 120px" />
      <n-button type="primary" @click="onOpen">
          <template #icon>
            <SvgIcon icon="akar-icons:plus" />
          </template>
        新增知识库
      </n-button>
      </n-space>
    </template>

    <div class="knowledge-list">
      <template v-if="filteredList.length > 0">
        <n-card v-for="item in filteredList" 
                :key="item.id" 
                class="knowledge-card" 
                :class="{ 'dark': isDarkTheme }">
          <template #header>
            <div class="card-header">
              <div class="header-main">
                <span class="title" :title="item.name">{{ item.name }}</span>
                <div class="tags">
                  <n-tag v-if="item.scopeConfig?.scopeType !== 'personal'" 
                        :type="item.scopeConfig?.permissionType === 'edit' ? 'warning' : 'success'" 
                        size="small">
                    {{ getPermissionTypeLabel(item.scopeConfig?.permissionType) }}
                  </n-tag>
                  <n-tag :type="item.scopeConfig?.scopeType === 'personal' ? 'success' : 'info'" 
                        size="small">
                    {{ getScopeTypeLabel(item.scopeConfig?.scopeType) }}
                  </n-tag>
                </div>
              </div>
            </div>
          </template>

          <div class="description" :title="item.description || '暂无描述'">
            {{ item.description || '暂无描述' }}
          </div>

          <template #footer>
            <div class="card-footer">
              <n-space justify="end">
                <n-button text type="success" v-show="item.documentCount > 0" @click="router.push(`/knowledge/chat/${item.id}?name=${item.name}`)">
                  <template #icon>
                    <SvgIcon icon="material-symbols:chat" />
                  </template>
                  问答对话
                </n-button>
                <n-button 
                  v-if="item.isCreator || item.scopeConfig?.permissionType != 'readOnly'"
                  text 
                  type="info" 
                  @click="router.push(`/knowledge/documents/${item.id}`)">
                  <template #icon>
                    <SvgIcon icon="material-symbols:description" />
                  </template>
                  文档管理
                </n-button>
                <n-button 
                  text 
                  type="primary" 
                  v-if="item.isCreator"
                  @click="detailModalRef.show('edit', item)">
                  <template #icon>
                    <SvgIcon icon="material-symbols:edit" />
                  </template>
                  编辑
                </n-button>
                <n-button text type="error" @click="handleDelete(item.id)" v-if="item.isCreator">
                  <template #icon>
                    <SvgIcon icon="material-symbols:delete" />
                  </template>
                  删除
                </n-button>
              </n-space>
            </div>
          </template>
        </n-card>
      </template>
      <div v-else class="empty_content">
        <n-empty description="没有知识库～" >
          <template #extra>
            <n-button type="primary" @click="onOpen">
                <template #icon>
                  <SvgIcon icon="akar-icons:plus" />
                </template>
              新增知识库
            </n-button>
          </template>
        </n-empty>
      </div>
    </div>

    <DetailModal ref="detailModalRef" @success="handleSuccess" />
  </CommonPage>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { NCard, NGrid, NGridItem, NButton, NInput, NSelect, NTag, NSpace } from 'naive-ui'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import api from './api'
import DetailModal from './DetailModal.vue'
import { useRouter } from 'vue-router'
import { useTheme } from '@/hooks/useTheme'

defineOptions({ name: 'KnowledgeBase' })

const router = useRouter()
const { isDarkTheme } = useTheme()
const detailModalRef = ref(null)
const searchText = ref('')
const filterType = ref(null)
const knowledgeList = ref([])

// 权限类型选项
const scopeTypeOptions = [
  { label: '个人', value: 'personal' },
  { label: '部门', value: 'team' },
]

// 权限范围选项
const permissionTypeOptions = [
  { label: '只读', value: 'readOnly' },
  { label: '编辑', value: 'edit' }
]

// 获取权限类型标签
const getScopeTypeLabel = (type) => {
  const option = scopeTypeOptions.find(item => item.value === type)
  return option ? option.label : '-'
}

// 获取权限范围标签
const getPermissionTypeLabel = (type) => {
  const option = permissionTypeOptions.find(item => item.value === type)
  return option ? option.label : '-'
}

// 过滤后的列表
const filteredList = computed(() => {
  let list = knowledgeList.value
  
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    list = list.filter(item => 
      item.name.toLowerCase().includes(search) || 
      item.description?.toLowerCase().includes(search)
    )
  }
  
  if (filterType.value) {
    list = list.filter(item => item.scopeConfig?.scopeType === filterType.value)
  }
  
  return list
})

// 打开新增弹窗
const onOpen = () => {
  detailModalRef.value?.show('create')
}

// 删除知识库
const handleDelete = async (id) => {
  $dialog.confirm({
    content: `确认删除`,
    async confirm() {
      try {
        await api.delete(id)
        $message.success('删除成功', { key: 'deleteMenu' })
        loadData()
      }
      catch (error) {
        $message.error('删除失败')
      }
    },
  })
}

// 加载数据
const loadData = async () => {
  try {
    const { data } = await api.read()
    knowledgeList.value = data.rows || []
  } catch (error) {
    $message.error('获取数据失败')
  }
}

// 刷新列表
const handleSuccess = () => {
  loadData()
}

// 屏幕宽度响应
const isSmallScreen = ref(false)

const updateScreenSize = () => {
  // 获取卡片的实际宽度，如果小于 400px 就允许换行
  const cards = document.querySelectorAll('.knowledge-card')
  if (cards.length > 0) {
    const cardWidth = cards[0].offsetWidth
    isSmallScreen.value = cardWidth < 400
  }
}

onMounted(() => {
  loadData()
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
})
</script>

<style lang="scss" scoped>
.knowledge-list {
  margin-top: 12px;
  padding: 0 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  .empty_content {
    width: 100%;
    height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.knowledge-card {
  width: calc(25% - 16px);
  min-width: 380px;
  height: 200px;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &.dark {
    background-color: var(--dify_bg_secondary);
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
  
  .card-header {
    .header-main {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .title {
        flex: 1;
        font-size: 16px;
        font-weight: 500;
        color: var(--dify_text_primary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .tags {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
  
  .description {
    flex: 1;
    margin: 12px 0;
    color: var(--dify_text_secondary);
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .card-footer {
    border-top: 1px solid var(--dify_border);
    padding-top: 12px;
    
    :deep(.n-space) {
      justify-content: flex-end;
      flex-wrap: nowrap;
      overflow-x: auto;
      
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
  
  :deep(.n-card-header) {
    padding: 16px;
    padding-bottom: 0;
  }
  
  :deep(.n-card__content) {
    padding: 0 16px;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.n-card__footer) {
    padding: 0 16px 16px;
    height: 60px;
  }
  
  :deep(.n-button) {
    padding: 4px 8px;
    white-space: nowrap;
    min-width: fit-content;
    
    .n-button__icon {
      margin-right: 4px;
    }
  }
}

</style>