import { request } from '@/utils'

export default {
  // 获取文档列表
  list: (data) => request.post('/v2/system/documents/list', data),

  // 获取文档详情
  detail: (params) => request.get('/v2/system/documents/detail', { params }),

  // 批量上传文档
  batchUpload: (data) => request.post('/v2/system/documents/batchUpload', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),

  // 删除文档
  deleteDocument: (params) => request.get('/v2/system/documents/delete', { params }),

  // 批量删除文档
  batchDelete: (data) => request.post('/v2/system/documents/batchDelete', data),

  // 检查文档处理状态
  checkStatus: (data) => request.post('/v2/system/documents/checkStatus', data),

  // 获取文档处理进度
  progress: (params) => request.get('/v2/system/documents/progress', { params }),

  // 重新处理文档
  reprocess: (data) => request.post('/v2/system/documents/reprocess', data),

  // 文件下载
  downloadFile: (params) => request.get(`/v2/system/download`, { params, responseType: 'blob' }),

  // 设置文档切片配置
  setChunksConfig: (data) => request.post('/v2/system/documents/chunks/config', data),

  // 获取文档切片配置
  getChunksConfig: (params) => request.get('/v2/system/documents/chunks/config', { params }),

}