# 分页逻辑重写说明

## 重写方案

### 问题分析
原来的问题是 n-data-table 内置分页组件显示总条数不正确，即使日志显示数据正确，但界面显示错误。

### 解决方案
**完全分离数据表格和分页组件**：
- n-data-table 设置 `:pagination="false"` 禁用内置分页
- 使用独立的 n-pagination 组件处理分页逻辑

## 新的代码结构

### 1. 分页状态管理
```javascript
// 简化的分页状态
const currentPage = ref(1)      // 当前页码
const pageSize = ref(5)         // 每页条数
const totalCount = ref(0)       // 总条数

// 分页前缀文本
const paginationPrefix = computed(() => {
  return `共 ${totalCount.value} 条`
})
```

### 2. 模板结构
```vue
<div class="document-list">
  <!-- 数据表格（禁用内置分页） -->
  <n-data-table
    :columns="columns"
    :data="documentList"
    :loading="loading"
    :pagination="false"
  />
  
  <!-- 独立的分页组件 -->
  <div class="pagination-wrapper">
    <n-pagination
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      :item-count="totalCount"
      :page-sizes="[5, 10, 20, 30, 40]"
      show-size-picker
      show-quick-jumper
      :prefix="paginationPrefix"
      @update:page="onPageChange"
      @update:page-size="onPageSizeChange"
    />
  </div>
</div>
```

### 3. 数据加载逻辑
```javascript
const loadData = async () => {
  try {
    loading.value = true
    
    const { data } = await api.list({
      knowledgeId,
      page: currentPage.value,
      size: pageSize.value
    })
    
    documentList.value = data.rows || []
    totalCount.value = data.total  // 直接设置总条数
    
  } catch (error) {
    console.error('加载文档列表失败:', error)
  } finally {
    loading.value = false
  }
}
```

### 4. 事件处理
```javascript
// 处理分页变化
const onPageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 处理页面大小变化
const onPageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1  // 重置到第一页
  loadData()
}
```

## 优势

1. **完全控制**：分页逻辑完全由我们控制，不依赖 n-data-table 的内置分页
2. **数据准确**：直接设置 `totalCount.value = data.total`，确保显示正确
3. **响应式更新**：使用 `ref` 确保响应式更新正常工作
4. **简化状态**：只需要维护三个简单的状态变量
5. **易于调试**：分页状态清晰可见，便于排查问题

## 测试验证

当接口返回 `total: 11` 时：
- `totalCount.value = 11`
- 分页组件显示 "共 11 条"
- 每页 5 条，共 3 页
- 分页按钮正确启用/禁用

## 调试信息

页面上会显示实时的分页状态：
```
分页状态: 当前页: 1 | 页面大小: 5 | 总条数: 11 | 前缀: 共 11 条
```

这样可以直观地看到分页数据是否正确设置。
