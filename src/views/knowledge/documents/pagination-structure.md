# 分页重构说明

## 重构前后对比

### 重构前（复杂结构）
```javascript
// 分页状态
const paginationState = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0
})

// 分页配置
const paginationConfig = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  disabled: false,
  simple: false
})

// 需要同步两个状态
```

### 重构后（简洁结构）
```javascript
// 统一的分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`
})

// 只需要维护一个状态
```

## 接口数据结构

### API 请求参数
```javascript
{
  knowledgeId: "xxx",
  page: 1,        // 当前页码
  size: 10        // 每页条数
}
```

### API 返回数据
```javascript
{
  code: 0,
  data: {
    rows: [...],    // 文档列表数据
    total: 11       // 总条数
  }
}
```

## 分页逻辑流程

1. **初始化**：设置默认分页参数
2. **数据加载**：根据分页参数调用API
3. **状态更新**：更新 `pagination.itemCount = data.total`
4. **用户交互**：处理分页变化事件
5. **重新加载**：调用 `loadData()` 获取新数据

## 事件处理

### 页码变化
```javascript
const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}
```

### 页面大小变化
```javascript
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1  // 重置到第一页
  loadData()
}
```

## 优势

1. **简化状态管理**：只需要维护一个 `pagination` 对象
2. **减少同步问题**：避免多个状态之间的同步错误
3. **符合标准**：直接使用 Naive UI 的标准分页配置
4. **易于维护**：代码结构清晰，逻辑简单
5. **性能优化**：减少不必要的响应式更新

## 测试验证

当接口返回 `total: 11` 时：
- 显示 "共 11 条"
- 每页 10 条，共 2 页
- 第1页：上一页禁用，下一页启用
- 第2页：上一页启用，下一页禁用
