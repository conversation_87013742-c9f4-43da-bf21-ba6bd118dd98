<template>
  <n-modal v-model:show="visible" preset="dialog" title="切片方法设置" style="width: 600px;">
    <n-form ref="formRef" :model="form" :rules="rules" label-placement="top">
      <n-form-item path="chunkSize">
        <template #label>
          <div style="display: flex; align-items: center;">
            <span>建议文本块大小</span>
            <n-popover trigger="hover" placement="top">
              <template #trigger>
                <SvgIcon icon="material-symbols:help-outline" style="margin-left: 4px; cursor: help; color: #999;" />
              </template>
              <div style="max-width: 300px; line-height: 1.5; font-size: 13px;">
                建议的生成文本块的 token 数阈值。如果切分得到的小文本段 token 数达不到这一阈值就会不断与之后的文本段合并，直至再合并下一个文本段会超过这一阈值为止，此时产生一个最终文本块。如果系统在切分文本段时始终没有遇到文本分段标识符，即便文本段 token 数已经超过这一阈值，系统也不会生成新文本块。
              </div>
            </n-popover>
          </div>
        </template>
        <n-input-number v-model:value="form.chunkSize" :min="1" :max="10000" style="width: 100%;" />
      </n-form-item>

      <n-form-item path="separator">
        <template #label>
          <div style="display: flex; align-items: center;">
            <span>文本分段标识符</span>
            <n-popover trigger="hover" placement="top">
              <template #trigger>
                <SvgIcon icon="material-symbols:help-outline" style="margin-left: 4px; cursor: help; color: #999;" />
              </template>
              <div style="max-width: 300px; line-height: 1.5; font-size: 13px;">
                支持多字符作为分隔符，多字符用两个反引号 `` 分隔符包裹。若配置成：\n`##`; 系统将首先使用换行符、两个#号以及分号先对文本进行分割，随后再对分得的小文本块按照「建议文本块大小」设定的大小进行拼装。在设置文本分段标识符前请确保理解上述文本分段切片机制。
              </div>
            </n-popover>
          </div>
        </template>
        <n-input v-model:value="form.separator" style="width: 100%;" />
      </n-form-item>

      <n-form-item path="keywordExtraction">
        <template #label>
          <div style="display: flex; align-items: center;">
            <span>自动关键词提取</span>
            <n-popover trigger="hover" placement="top">
              <template #trigger>
                <SvgIcon icon="material-symbols:help-outline" style="margin-left: 4px; cursor: help; color: #999;" />
              </template>
              <div style="max-width: 300px; line-height: 1.5; font-size: 13px;">
                自动为每个文本块中提取 N 个关键词，用以提升查询精度。请注意：该功能采用"系统模型设置"中设置的默认聊天模型提取关键词，因此也会产生更多 Token 消耗。另外，你也可以手动更新生成的关键词。
              </div>
            </n-popover>
          </div>
        </template>
        <n-input-number v-model:value="form.keywordExtraction" :min="0" :max="30" style="width: 100%;" />
      </n-form-item>

      <n-form-item path="questionExtraction">
        <template #label>
          <div style="display: flex; align-items: center;">
            <span>自动问题提取</span>
            <n-popover trigger="hover" placement="top">
              <template #trigger>
                <SvgIcon icon="material-symbols:help-outline" style="margin-left: 4px; cursor: help; color: #999;" />
              </template>
              <div style="max-width: 300px; line-height: 1.5; font-size: 13px;">
                利用"系统模型设置"中设置的 chat model 对知识库的每个文本块提取 N 个问题以提高其排名得分。请注意，开启后将消耗额外的 token。您可以在块列表中查看、编辑结果。如果自动问题提取发生错误，不会妨碍整个分块过程，只会将空结果添加到原始文本块。
              </div>
            </n-popover>
          </div>
        </template>
        <n-input-number v-model:value="form.questionExtraction" :min="0" :max="10" style="width: 100%;" />
      </n-form-item>
    </n-form>

    <template #action>
      <div style="display: flex; justify-content: flex-end; gap: 12px;">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="loading">确定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { NModal, NForm, NFormItem, NInput, NInputNumber, NPopover, NButton } from 'naive-ui'
import SvgIcon from '@/components/common/SvgIcon/index.vue'
import api from './api'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  document: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const visible = ref(false)
const formRef = ref(null)
const loading = ref(false)

// 表单数据
const form = ref({
  chunkSize: 512,
  separator: '\\n',
  keywordExtraction: 0,
  questionExtraction: 0
})

// 表单验证规则
const rules = {
  chunkSize: [
    { required: true, message: '请输入建议文本块大小' },
    { type: 'number', min: 1, max: 10000, message: '文本块大小必须在1-10000之间', trigger: 'blur' }
  ],
  separator: [
    { required: true, message: '请输入文本分段标识符', trigger: 'blur' }
  ],
  keywordExtraction: [
    { type: 'number', min: 0, max: 30, message: '关键词提取数量必须在0-30之间', trigger: 'blur' }
  ],
  questionExtraction: [
    { type: 'number', min: 0, max: 10, message: '问题提取数量必须在0-10之间', trigger: 'blur' }
  ]
}

// 监听 modelValue 变化
watch(() => props.modelValue, async (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置表单为默认值
    resetForm()
    // 如果有文档ID，尝试获取现有配置
    if (props.document?.id) {
      await loadExistingConfig()
    }
  }
})

// 监听 visible 变化，同步到父组件
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  form.value = {
    chunkSize: 512,
    separator: '\\n',
    keywordExtraction: 0,
    questionExtraction: 0
  }
}

// 加载现有配置
const loadExistingConfig = async () => {
  try {
    const { data } = await api.getChunksConfig({ documentId: props.document.id })
    if (data) {
      form.value = {
        chunkSize: data.chunkSize || 512,
        separator: data.separator || '\\n',
        keywordExtraction: data.keywordExtraction || 0,
        questionExtraction: data.questionExtraction || 0
      }
    }
  } catch (error) {
    console.error('获取切片配置失败:', error)
    // 如果获取失败，使用默认值
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 调用切片设置API
    const params = {
      documentId: props.document?.id,
      chunkSize: form.value.chunkSize,
      separator: form.value.separator,
      keywordExtraction: form.value.keywordExtraction,
      questionExtraction: form.value.questionExtraction
    }
    
    await api.setChunksConfig(params)
    
    $message.success('切片设置保存成功')
    visible.value = false
    emit('success')
    
  } catch (error) {
    console.error('切片设置失败:', error)
    $message.error('切片设置失败')
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  show: async (document) => {
    resetForm()
    visible.value = true
    // 如果传入了文档，尝试加载现有配置
    if (document?.id) {
      await loadExistingConfig()
    }
  }
})
</script>
