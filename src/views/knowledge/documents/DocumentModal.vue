<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="false"
    preset="dialog"
    title="上传文档"
    :style="{ width: '600px' }"
  >
    <div class="upload-container">
      <n-upload
        ref="uploadRef"
        :max="10"
        :accept="acceptTypes"
        directory-dnd
        multiple
        :default-upload="false"
        :show-file-list="false"
        :file-list="[]"
        @change="handleChange"
      >
        <div class="upload-trigger">
          <n-upload-dragger>
            <div class="upload-dragger-content">
              <SvgIcon icon="material-symbols:cloud-upload" size="48" />
              <p class="upload-text">点击或拖拽文件到此处上传</p>
              <p class="upload-hint">支持 .pdf, .doc, .docx, .txt, .md, .html 等格式，最多可同时上传10个文件</p>
            </div>
          </n-upload-dragger>
        </div>
      </n-upload>

      <!-- 文件列表 -->
      <div v-if="fileList.length" class="file-list">
        <div v-for="file in fileList" :key="file.id" class="file-item">
          <div class="file-info">
            <SvgIcon :icon="getFileIcon(file.name)" />
            <span class="file-name" :title="file.name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.file?.size) }}</span>
          </div>
          <div class="file-status">
            <template v-if="file.status === 'pending'">
              <n-button text type="error" @click="removeFile(file)">
                <template #icon>
                  <SvgIcon icon="material-symbols:delete" />
                </template>
              </n-button>
            </template>
            <template v-else-if="file.status === 'uploading'">
              <n-progress
                type="line"
                :percentage="file.percentage"
                :show-indicator="false"
                :height="2"
                style="width: 60px"
              />
            </template>
            <template v-else-if="file.status === 'finished'">
              <n-tag type="success" size="small">上传成功</n-tag>
            </template>
            <template v-else-if="file.status === 'error'">
              <n-tag type="error" size="small">上传失败</n-tag>
            </template>
          </div>
        </div>
      </div>
    </div>

    <template #action>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="uploading"
          :disabled="!fileList.length"
          @click="handleUpload"
        >
          {{ uploading ? '上传中...' : '开始上传' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { NModal, NUpload, NUploadDragger, NButton, NSpace, NProgress, NTag } from 'naive-ui'
import SvgIcon from '@/components/common/SvgIcon/index.vue'
import api from './api'

defineOptions({ name: 'DocumentModal' })

const props = defineProps({
  knowledgeId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['success'])


const visible = ref(false)
const uploadRef = ref(null)
const fileList = ref([])
const uploading = ref(false)

// 支持的文件类型
const acceptTypes = '.pdf,.doc,.docx,.txt,.md,.html'

// 根据文件扩展名获取图标
const getFileIcon = (fileName) => {
  if (!fileName) return 'material-symbols:file'
  
  const parts = fileName.split('.')
  const ext = parts.length > 1 ? parts[parts.length - 1].toLowerCase() : ''
  
  switch (ext) {
    case 'pdf':
      return 'material-symbols:picture-as-pdf'
    case 'doc':
    case 'docx':
      return 'material-symbols:article'
    case 'txt':
    case 'md':
      return 'material-symbols:description'
    default:
      return 'material-symbols:file'
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${Number(size).toFixed(2)} ${units[index]}`
}

// 文件变化处理
const handleChange = ({ fileList: newFileList }) => {
  // 由于我们设置了 :file-list="[]"，这里的 newFileList 应该只包含新添加的文件
  // 直接添加到我们的文件列表中
  const filesToAdd = newFileList.map(file => ({
    ...file,
    status: 'pending',
    percentage: 0
  }))

  fileList.value.push(...filesToAdd)
}

// 移除文件
const removeFile = (file) => {
  const index = fileList.value.indexOf(file)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 开始上传
const handleUpload = async () => {
  if (!fileList.value.length) return
  
  uploading.value = true
  try {
    const formData = new FormData()
    formData.append('autoProcess', true)
    fileList.value.forEach(file => {
      // 确保使用原始文件对象
      const rawFile = file.file || file
      formData.append('files', rawFile)
    })
    formData.append('knowledgeId', props.knowledgeId)

    await api.batchUpload(formData, (e) => {
      if (e.lengthComputable) {
        const percentage = Math.round((e.loaded * 100) / e.total)
        // 更新所有文件的进度
        fileList.value.forEach(file => {
          file.status = 'uploading'
          file.percentage = percentage
        })
      }
    })
    
    // 更新所有文件状态为完成
    fileList.value.forEach(file => {
      file.status = 'finished'
      file.percentage = 100
    })
    
    $message.success('上传成功')
    emit('success')
    handleCancel()
  } catch (error) {
    // 更新所有文件状态为错误
    fileList.value.forEach(file => {
      file.status = 'error'
    })
    $message.error('上传失败')
  } finally {
    uploading.value = false
  }
}

// 取消上传
const handleCancel = () => {
  clearFileList()
  visible.value = false
}

// 显示弹窗
const show = () => {
  visible.value = true
}

// 清理文件列表和上传组件状态
const clearFileList = () => {
  fileList.value = []
}

// 监听弹框关闭，清理状态
watch(visible, (newVal) => {
  if (!newVal) {
    // 弹框关闭时清理文件列表
    clearFileList()
  }
})

defineExpose({
  show
})
</script>

<style lang="scss" scoped>
.upload-container {
  .upload-trigger {
    margin-bottom: 16px;
    
    .upload-dragger-content {
      padding: 32px;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: var(--dify_text_secondary);
      
      .upload-text {
        margin: 16px 0 8px;
        font-size: 16px;
        color: var(--dify_text_primary);
      }
      
      .upload-hint {
        font-size: 14px;
      }
    }
  }
  
  .file-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--dify_border);
    border-radius: 8px;
    
    .file-item {
      padding: 12px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid var(--dify_border);
      
      &:last-child {
        border-bottom: none;
      }
      
      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;
        
        .file-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: var(--dify_text_primary);
        }
        
        .file-size {
          font-size: 12px;
          color: var(--dify_text_secondary);
        }
      }
      
      .file-status {
        flex-shrink: 0;
        width: 80px;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

:deep(.n-upload-trigger) {
  width: 100%;
}

:deep(.n-upload-dragger) {
  border-style: dashed !important;
}
</style> 