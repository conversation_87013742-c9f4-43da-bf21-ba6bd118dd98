<template>
  <CommonPage>
    <template #header>
      <div class="flex items-center justify-between" style="width: 100%">
        <div class="flex items-center">
          <n-button quaternary @click="router.back()">
            <template #icon>
              <SvgIcon icon="material-symbols:arrow-back" />
            </template>
          </n-button>
          <span class="ml-2">文档管理</span>
        </div>
        <div style="display: flex;">
          <n-button type="primary" @click="loadData">
            刷新
          </n-button>

          <n-button type="primary" @click="onOpenDocument" style="margin-left: 8px;">
            <template #icon>
              <SvgIcon icon="material-symbols:add" />
            </template>
            上传文档
          </n-button>
        </div>
      </div>
    </template>

    <div class="document-list">
      <n-data-table
        :columns="columns"
        :data="documentList"
        :loading="loading"
        :pagination="false"
      />

      <!-- 自定义分页组件 -->
      <div class="pagination-wrapper" style="display: flex; justify-content: flex-end; margin-top: 16px;">
        <n-pagination
          v-model:page="currentPage"
          v-model:page-size="pageSize"
          :item-count="totalCount"
          :page-sizes="[10, 20, 30, 40]"
          show-size-picker
          show-quick-jumper
          :prefix="paginationPrefix"
          @update:page="onPageChange"
          @update:page-size="onPageSizeChange"
        />
      </div>
    </div>

    <DocumentModal ref="documentModalRef" :knowledgeId="knowledgeId" @success="handleSuccess" />

    <!-- 切片设置弹框 -->
    <ChunksSettingModal
      v-model="chunksModalVisible"
      :document="currentDocument"
      @success="handleChunksSuccess"
    />
  </CommonPage>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NButton, NDataTable, NTag, NProgress, NPagination } from 'naive-ui'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import api from './api'
import DocumentModal from './DocumentModal.vue'
import ChunksSettingModal from './ChunksSettingModal.vue'

defineOptions({ name: 'KnowledgeDocuments' })

const route = useRoute()
const router = useRouter()
const knowledgeId = route.params.id

const documentModalRef = ref(null)
const loading = ref(false)
const documentList = ref([])
const refreshTimer = ref(null)
// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 分页前缀函数
const paginationPrefix = ({ itemCount }) => {
  return `共 ${itemCount} 条`
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  return `${Number(size).toFixed(2)} ${units[unitIndex]}`
}

// 获取状态类型
const getStatusType = (status) => {
  const map = {
    processing: 'info',
    completed: 'success',
    failed: 'error'
  }
  return map[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const map = {
    processing: '解析中',
    completed: '已完成',
    failed: '失败'
  }
  return map[status] || status
}

// 打开上传文档弹窗
const onOpenDocument = () => {
  documentModalRef.value?.show(knowledgeId)
}

// 切片设置相关
const chunksModalVisible = ref(false)
const currentDocument = ref(null)

// 打开切片设置弹框
const handleChunksSettings = (document) => {
  currentDocument.value = document
  chunksModalVisible.value = true
}

// 切片设置成功回调
const handleChunksSuccess = () => {
  // 可以在这里刷新数据或执行其他操作
  console.log('切片设置成功')
}

// 删除文档
const handleDelete = async (id) => {
  $dialog.confirm({
    content: `确认删除`,
    async confirm() {
      try {
        await api.deleteDocument({
          knowledgeId: knowledgeId,
          docId: id,
        })
        $message.success('删除成功')
        loadData()
      }
      catch (error) {
        $message.error('删除失败')
      }
    },
  })
}
// 下载文档
const handerDownloadFile = async (info) => {
  try {
    const res = await api.downloadFile({
      bucket: 'knowledge-documents',
      objectName: info.filePath,
      filename: info.name,
    })
    // 创建一个Blob对象
    const blob = new Blob([res], { type: res.type || 'application/octet-stream' })
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = info.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    $message.error('下载失败')
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const { data } = await api.list({
      knowledgeId,
      page: currentPage.value,
      size: pageSize.value
    })

    documentList.value = data.rows || []
    totalCount.value = data.total

    // 检查是否所有文档都是completed状态
    const allCompleted = documentList.value.every(doc => doc.status === 'completed')
    if (allCompleted) {
      stopAutoRefresh() // 如果全部完成，停止自动刷新
    }
  } catch (error) {
    $message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 启动自动刷新
const startAutoRefresh = () => {
  if (!refreshTimer.value) {
    refreshTimer.value = setInterval(() => {
      loadData()
    }, 3000)
  }
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 处理分页变化
const onPageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 处理页面大小变化
const onPageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}



// 上传成功回调
const handleSuccess = () => {
  loadData()
  startAutoRefresh() // 上传成功后启动自动刷新
}

const columns = [
  { 
    title: '文档名称', 
    key: 'name', 
    ellipsis: { tooltip: true }
  },
  { 
    title: '上传时间', 
    key: 'createTime',
    width: 180,
    render(row) {
      return formatDate(row.createTime)
    }
  },
  { 
    title: '上传人', 
    key: 'uploadUserName',
    width: 180,
    render(row) {
      return row.uploadUserName
    }
  },
  { 
    title: '文件大小', 
    key: 'size',
    render(row) {
      return formatFileSize(row.size)
    }
  },
  { 
    title: '分块数', 
    key: 'chunkCount',
    render(row) {
      return row.chunkCount
    }
  },
  {
    title: '解析进度',
    key: 'progress',
    render(row) {
      const progress = row.progress || 0
      const type = progress >= 100 ? 'success' : 'info'
      const showIndicator = progress >= 100

      return h(NProgress, {
        type: 'line',
        percentage: progress,
        indicatorPlacement: 'inside',
        processing: progress < 100,
        showIndicator,
        status: type,
        height: 14,
      })
    }
  },
  { 
    title: '状态', 
    key: 'status',
    render(row) {
      return h(
        NTag,
        { type: getStatusType(row.status) },
        { default: () => getStatusText(row.status) }
      )
    }
  },
  {
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return h(
        'div',
        { style: 'display: flex; align-items: center; justify-content: flex-end;' },
        [
          // h(
          //   NButton,
          //   {
          //     size: 'small',
          //     onClick: () => handleChunksSettings(row),
          //   },
          //   {
          //     default: () => '切片方法',
          //   }
          // ),
          h(
            NButton,
            {
              size: 'small',
              style: 'margin-left:8px;',
              onClick: () => handerDownloadFile(row),
            },
            {
              default: () => '下载',
            }
          ),
          h(
            NButton,
            {
              size: 'small',
              type: 'error',
              style: 'margin-left:8px;',
              onClick: () => handleDelete(row.id),
            },
            {
              default: () => '删除',
              icon: () => h(SvgIcon, { icon: 'mi:delete' }),
            }
          ),
        ]
      )
    },
  },
]

onMounted(() => {
  console.log('组件挂载，初始分页配置:', {
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    totalCount: totalCount.value
  })
  loadData()
})

onUnmounted(() => {
  stopAutoRefresh() // 组件卸载时停止自动刷新
})
</script>

<style lang="scss" scoped>
.document-list {
  margin-top: 16px;
}
</style> 