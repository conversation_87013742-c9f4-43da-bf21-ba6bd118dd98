import { request } from '@/utils'

export default {
  create: data => request.post('/v2/system/knowledge/create', data),
  read: (data = {}) => request.post('/v2/system/knowledge/list', data),
  update: data => request.post(`/v2/system/knowledge/update`, data),
  delete: id => request.get(`/v2/system/knowledge/delete?id=${id}`),
  getDepartments: ({} = {}) => request.get('/v2/system/user/getCurrentUserDeptInfo'),
  getUsers: (data = {}) => request.post('/v2/system/user/list', data),
}
