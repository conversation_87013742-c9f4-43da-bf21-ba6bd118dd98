import { request } from '@/utils'
import { useAuthStore } from '@/store'

const { access_token } = useAuthStore()
const Authorization = `Bearer ${access_token}`
const baseApi = import.meta.env.VITE_BASE_API 

export default {
  // 发送流式聊天消息
  chatStream: async (data) => {
    const response = await fetch(`/v2/system/chat/message/sendStream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        authorization: Authorization
      },
      body: JSON.stringify(data),
    })
    return response.body.getReader()
  },

  // 获取聊天会话列表
  getChatList: (data) => request.post('/v2/system/chat/session/list', data),
  // 创建新会话
  creatChat: (data) => request.post('/v2/system/chat/session/create', data),
  // 删除会话
  deleteChat: (params) => request.get('/v2/system/chat/session/delete', { params }),
  
} 