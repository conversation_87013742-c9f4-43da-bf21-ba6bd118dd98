<template>
  <!-- 查看详情 -->
  <n-modal
    v-model:show="modalShow"
    :show-icon="false"
    preset="dialog"
    :title="modalTitle"
    :mask-closable="false"
    style="width: 720px"
    transform-origin="center"
  >
    <n-form
      ref="modalFormRef"
      :model="modalForm"
      :rules="rules"
      label-placement="left"
      label-align="left"
      :label-width="100"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="知识库名称" path="name">
        <n-input v-model:value="modalForm.name" placeholder="请输入知识库名称" />
      </n-form-item>

      <n-form-item label="描述" path="description">
        <n-input type="textarea" v-model:value="modalForm.description" placeholder="请输入知识库描述" />
      </n-form-item>

      <n-form-item label="权限类型" path="scopeConfig.scopeType" required>
        <n-select v-model:value="modalForm.scopeConfig.scopeType" :options="scopeTypeOptions" placeholder="请选择权限类型" />
      </n-form-item>

      <template v-if="modalForm.scopeConfig.scopeType !== 'personal'">
        <n-form-item label="权限范围" path="scopeConfig.permissionType" required>
          <n-select v-model:value="modalForm.scopeConfig.permissionType" :options="permissionTypeOptions" placeholder="请选择权限范围" />
        </n-form-item>

        <div style="background: #f6f8fa; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <div style="color: #666; font-size: 13px; margin-bottom: 8px;">
            <span style="color: #f56565;">*</span> 请至少选择一个部门或人员
          </div>

          <n-form-item label="选择部门" path="scopeConfig.departmentIds" :show-feedback="false">
            <n-tree-select v-model:value="modalForm.scopeConfig.departmentIds" multiple :options="departmentOptions"
              label-field="deptName" key-field="id" placeholder="请选择部门" clearable />
          </n-form-item>

          <n-form-item label="选择人员" path="scopeConfig.userIds" :show-feedback="false" style="margin-top: 12px;">
            <n-select v-model:value="modalForm.scopeConfig.userIds" multiple :options="userOptions"
              label-field="username" value-field="id" placeholder="请选择人员" />
          </n-form-item>
        </div>
      </template>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleConfirm">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { NModal, NForm, NFormItem, NInput, NSelect, NButton, NSpace, NTreeSelect } from 'naive-ui'
import api from './api'
import { useUserStore } from '@/store'

const emit = defineEmits(['success'])
const userStore = useUserStore()

const modalShow = ref(false)
const loading = ref(false)
const modalAction = ref('create')
const modalFormRef = ref(null)

const modalForm = ref({
  id: null,
  name: '',
  description: '',
  scopeConfig: {
    scopeType: 'personal',
    permissionType: 'readOnly',
    departmentIds: [],
    userIds: []
  }
})

const modalTitle = computed(() => {
  return modalAction.value === 'create' ? '新增知识库' : '编辑知识库'
})

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入知识库名称',
    trigger: ['blur', 'input']
  },
  description: {
    required: true,
    message: '请输入知识库描述',
    trigger: ['blur', 'input']
  },
  'scopeConfig.scopeType': {
    required: true,
    message: '请选择权限类型',
    trigger: ['blur', 'change']
  },
  'scopeConfig.permissionType': {
    required: true,
    validator: (rule, value) => {
      // 只有当权限类型不是个人时才需要验证权限范围
      if (modalForm.value.scopeConfig.scopeType !== 'personal' && !value) {
        return new Error('请选择权限范围')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  'scopeConfig.departmentIds': {
    validator: (rule, value) => {
      // 只有当权限类型不是个人时才需要验证
      if (modalForm.value.scopeConfig.scopeType !== 'personal') {
        const hasDepartments = value && value.length > 0
        const hasUsers = modalForm.value.scopeConfig.userIds && modalForm.value.scopeConfig.userIds.length > 0

        if (!hasDepartments && !hasUsers) {
          return new Error('请至少选择一个部门或人员')
        }
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  'scopeConfig.userIds': {
    validator: (rule, value) => {
      // 只有当权限类型不是个人时才需要验证
      if (modalForm.value.scopeConfig.scopeType !== 'personal') {
        const hasUsers = value && value.length > 0
        const hasDepartments = modalForm.value.scopeConfig.departmentIds && modalForm.value.scopeConfig.departmentIds.length > 0

        if (!hasUsers && !hasDepartments) {
          return new Error('请至少选择一个部门或人员')
        }
      }
      return true
    },
    trigger: ['blur', 'change']
  }
}

// 部门选项
const departmentOptions = ref([])

// 用户选项
const userOptions = ref([])

// 权限类型选项
const scopeTypeOptions = [
  { label: '个人', value: 'personal' },
  { label: '部门', value: 'team' },
]

// 权限范围选项
const permissionTypeOptions = [
  { label: '只读', value: 'readOnly' },
  { label: '编辑', value: 'edit' }
]

// 获取当前用户的companyId
let userCompanyId = ref('')
const getUserCompanyId = async () => {
  userCompanyId.value = userStore.userInfo.companyId
}

// 获取部门列表
const getDepartments = async () => {
  try {
    const { data } = await api.getDepartments()
    departmentOptions.value = [data]
  } catch (error) {
    $message.error('获取部门列表失败')
  }
}

// 获取用户列表
const getUsers = async () => {
  try {
    const { data } = await api.getUsers({
      companyId: userCompanyId.value,
      page: 1,
      size: 999999
    })
    userOptions.value = data.rows
  } catch (error) {
    $message.error('获取用户列表失败')
  }
}

// 打开弹窗
const show = async (action, row = {}) => {
  modalAction.value = action
  
  // 重置表单
  modalForm.value = {
    id: null,
    name: '',
    description: '',
    scopeConfig: {
      scopeType: 'personal',
      permissionType: 'readOnly',
      departmentIds: [],
      userIds: []
    }
  }
  
  // 如果是编辑，填充数据
  if (action === 'edit' && row) {
    modalForm.value = { 
      ...row,
      scopeConfig: {
        ...row.scopeConfig,
        departmentIds: row.scopeConfig?.departmentIds || [],
        userIds: row.scopeConfig?.userIds || []
      }
    }
  }
  
  // 先显示弹窗，再加载数据
  modalShow.value = true
  
  // 异步加载数据
  await Promise.all([
    getDepartments(),
    getUsers()
  ])
}

// 取消
const handleCancel = () => {
  modalShow.value = false
  modalFormRef.value?.restoreValidation()
}

// 确认
const handleConfirm = async () => {
  try {
    await modalFormRef.value?.validate()

    // 额外验证：当权限类型不是个人时，确保至少选择了部门或人员
    if (modalForm.value.scopeConfig.scopeType !== 'personal') {
      const hasDepartments = modalForm.value.scopeConfig.departmentIds && modalForm.value.scopeConfig.departmentIds.length > 0
      const hasUsers = modalForm.value.scopeConfig.userIds && modalForm.value.scopeConfig.userIds.length > 0

      if (!hasDepartments && !hasUsers) {
        $message.error('请至少选择一个部门或人员')
        return
      }
    }
  } catch (error) {
    // 验证失败时显示友好提示
    $message.error('请完善必填信息')
    return
  }

  loading.value = true
  try {
    if (modalAction.value === 'create') {
      await api.create(modalForm.value)
      $message.success('创建成功')
    } else {
      await api.update(modalForm.value)
      $message.success('更新成功')
    }
    modalShow.value = false
    emit('success')
  } catch (error) {
    $message.error(modalAction.value === 'create' ? '创建失败' : '更新失败')
  } finally {
    loading.value = false
  }
}

// 监听权限类型变化，重新验证相关字段
watch(() => modalForm.value.scopeConfig.scopeType, (newType) => {
  // 当权限类型变为个人时，清空部门和人员选择
  if (newType === 'personal') {
    modalForm.value.scopeConfig.permissionType = 'readOnly'
    modalForm.value.scopeConfig.departmentIds = []
    modalForm.value.scopeConfig.userIds = []
  }

  // 重新验证相关字段
  modalFormRef.value?.validate('scopeConfig.permissionType').catch(() => {})
  modalFormRef.value?.validate('scopeConfig.departmentIds').catch(() => {})
  modalFormRef.value?.validate('scopeConfig.userIds').catch(() => {})
})

// 监听部门选择变化，重新验证人员字段
watch(() => modalForm.value.scopeConfig.departmentIds, () => {
  modalFormRef.value?.validate('scopeConfig.userIds').catch(() => {})
}, { deep: true })

// 监听人员选择变化，重新验证部门字段
watch(() => modalForm.value.scopeConfig.userIds, () => {
  modalFormRef.value?.validate('scopeConfig.departmentIds').catch(() => {})
}, { deep: true })

onMounted(async () => {
  await getUserCompanyId()
})

defineExpose({
  show
})
</script>

<style lang="scss" scoped>
.file-item {
  transition: all 0.3s ease;
}

:deep(.n-modal) {
  background-color: var(--dify_bg_common) !important;
  
  .n-modal-header {
    color: var(--dify_text_primary) !important;
  }
}

:deep(.n-form-item-label) {
  color: var(--dify_text_secondary) !important;
}

:deep(.n-input) {
  background-color: var(--dify_bg_common) !important;
  
  .n-input__input-el {
    color: var(--dify_text_primary) !important;
  }
}

:deep(.n-select) {
  .n-base-selection {
    background-color: var(--dify_bg_common) !important;
  }
  
  .n-base-selection-input {
    color: var(--dify_text_primary) !important;
  }
}

:deep(.n-tree-select) {
  .n-base-selection {
    background-color: var(--dify_bg_common) !important;
  }
  
  .n-base-selection-input {
    color: var(--dify_text_primary) !important;
  }
}
</style>