
import { request } from '@/utils'

export default {
  companyList: (data = {}) => request.get('/v2/system/dashboard/companyList', data),
  overview: companyId => request.get(`/v2/system/dashboard/overview`, { params: { companyId } }),
  dashboardRead: (data = {}) => request.post('/v2/system/dashboard/pageList', data),
  forms: companyId => request.get(`/v2/system/dashboard/forms`, { params: { companyId } }),
}
