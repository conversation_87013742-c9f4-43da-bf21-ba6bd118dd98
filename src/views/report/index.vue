<template>
  <div class="report_page">
    <div class="search_box" v-if="isAdmin">
        <n-select
          v-model:value="searchForm.companyId"
          filterable
          class="search_item"
          placeholder="选择公司"
          :options="companyOptions"
        />
        <NButton type="primary" @click="searchSubmit">
          搜索
        </NButton>
    </div>

    <n-card title="数据总览" class="card_box">
        <div class="card_item_content">
            <div class="card_item" v-for="item in cardData" :key="item.id" :style="{background: item.bgColor}">
                <div class="border_split" :style="{background: item.color}"></div>
                <div class="card_item_body">
                    <div class="label">{{ item.label }}</div>
                    <div class="value">{{ item.value }}</div>
                </div>
            </div>
        </div>
    </n-card>

    <div class="echarts_box">
        <n-card class="echarts_item">
            <MyChart :option="loginFormEchartsData">
            </MyChart>
        </n-card>
        <n-card class="echarts_item">
            <MyChart :option="groupTopEchartsData">
            </MyChart>
        </n-card>
        <n-card class="echarts_item">
            <MyChart :option="groupFormEchartsData">
            </MyChart>
        </n-card>
        <n-card class="echarts_item">
            <MyChart :option="agentTopEchartsData">
            </MyChart>
        </n-card>

    </div>
  </div>
</template>
<script setup>
import { onMounted } from 'vue'
import MyChart from '@/components/echarts/index.vue'
import api from './api'
import { loginFormEcharts, groupFormEcharts, groupTopEcharts, agentTopEcharts } from './echartsData'
import { useUserStore } from '@/store'

const userStore = useUserStore()

onMounted(async () => {
  await isAdminRole()
  if (isAdmin.value) {
    getCompanyList()
  } else {
    await getUserCompanyId()
    searchForm.companyId = userCompanyId
    searchSubmit()
  }
})

// 是不是平台管理员
let isAdmin = ref(false)
const isAdminRole = () => {
  isAdmin.value = userStore.roles[0].isAdmin == 1
}

// 不是公司管理员，获取当前用户的companyId
let userCompanyId = ref('')
const getUserCompanyId = async () => {
  userCompanyId.value = userStore.userInfo.companyId
}

let searchForm = reactive({
  companyId: undefined
})

let companyOptions = ref([])
const getCompanyList = async () => {
  let { data } = await api.companyList()
  companyOptions.value = data.map(item => {
    return {
      label: item.name,
      value: item.id
    }
  })
  if (!companyOptions.value.length) return $message.warning('请选择公司')
  searchForm.companyId = companyOptions.value[0].value
  searchSubmit()
}

let cardData = reactive([
  {id: 'loginCount', label: '当日用户登录数', value: 0, color: '#3bbf8b', bgColor: '#eaf9f6'},
  {id: 'useCount', label: '当日平台使用次数', value: 0, color: '#317df2', bgColor: '#edf3ff'},
  {id: 'avgLoginDuration', label: '用户平均登录使用时长', value: 0, color: '#fab301', bgColor: '#fff8e6'},
  {id: 'agentUseTimes', label: '智能体点击量', value: 0, color: '#7f5df2', bgColor: '#f3effe'},
  {id: 'tokenUse', label: '消耗token数量', value: 0, color: '#3bbf8b', bgColor: '#eaf9f6'},
])

const searchSubmit = async () => {
  if (!searchForm.companyId) return $message.warning('请选择公司')
  let { data } = await api.overview(searchForm.companyId)
  setDashboardData(data)
}

// 获取公司的系数
const setDashboardData = async (cardDataInfo) => {
  let { data } = await api.dashboardRead({
    companyId: searchForm.companyId,
    page: 1,
    size: 10
  })
  let dashboardNum = data.rows[0]
  if (!dashboardNum) {
    dashboardNum = {
      uv: 1,
      pv: 1,
      loginTime: 1,
      agentClick: 1,
      tokenUse: 1
    }
  }
  cardData.forEach(cardItem => {
    switch (cardItem.id) {
      case 'loginCount':
        cardItem.value = cardDataInfo.loginCount * dashboardNum['uv']
        break
      case 'useCount':
        cardItem.value = cardDataInfo.useCount * dashboardNum['pv']
        break
      case 'avgLoginDuration':
        cardItem.value = cardDataInfo.avgLoginDuration * dashboardNum['loginTime']
        break
      case 'agentUseTimes':
      cardItem.value = cardDataInfo.agentUseTimes * dashboardNum['agentClick']
        break
      case 'tokenUse':
      cardItem.value = dashboardNum['tokenUse']
        break
    }
  });
  getEchartsData(dashboardNum)
}

// echarts图表数据
let loginFormEchartsData = ref(loginFormEcharts)
let groupTopEchartsData = ref(groupTopEcharts)
let groupFormEchartsData = ref(groupFormEcharts)
let agentTopEchartsData = ref(agentTopEcharts)

const getEchartsData = async (dashboardNum) => {
  let { data } = await api.forms(searchForm.companyId)
  // 该公司一周内每日平台登录使用次数统计表
  loginFormEchartsData.value.xAxis.data = data.loginForm.map(item => {return item.name})
  loginFormEchartsData.value.series[0].data = data.loginForm.map(item => {
    item.value = item.value * dashboardNum.uv
    return item.value
  })

  // 智能体大厅分类点击量排名top5
  groupTopEchartsData.value.xAxis.data = data.groupTop.map(item => {return item.name})
  groupTopEchartsData.value.series[0].data = data.groupTop.map(item => {
    item.value = item.value * dashboardNum.agentClick
    return item.value
  })

  // 智能体大厅分类点击量占比
  groupFormEchartsData.value.series[0].data = data.groupForm.map(item => { 
    item.value = item.value * dashboardNum.agentClick
    return item
  })


  // 智能体点击量排名top5
  agentTopEchartsData.value.series[0].data = data.agentTop.map(item => { 
    item.value = item.value * dashboardNum.agentClick
    return item
  })
}



</script>

<style lang="scss" scoped>
.report_page {
    padding: 20px 16px;
    .search_box {
        display: flex;
        margin-bottom: 16px;
        .search_item {
            width: 300px;
            margin-right: 16px;
        }
        .time {
            width: 280px;
        }
    }
    .card_box {
        width: 100%;
        .card_item_content {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            .card_item {
                width: calc(25% - 16px);
                margin-right: 16px;
                margin-bottom: 20px;
                border-radius: 0 0 8px 8px;
                .border_split {
                    width: 100%;
                    height: 2px;
                }
                .card_item_body {
                    padding: 16px;
                    .label {
                        font-size: 18px;
                        font-weight: 500;
                        color: #333333;
                    }
                    .value {
                        margin-top: 16px;
                        font-size: 32px;
                        font-weight: 500;
                        color: #333333;
                    }
                }
            }
            .card_item:last-child {
                margin-right: 0px;
            }
        }
    }
    .echarts_box {
        width: 100%;
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .echarts_item {
            width: calc(50% - 8px);
            margin-bottom: 16px;
            height: 400px;
        }
    }
}
</style>