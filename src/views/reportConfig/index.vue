<template>
  <CommonPage>
    <template #action>
      <!-- <NButton v-permission="'AddUser'" type="primary" @click="handleAdd()"> -->
      <div class="flex gap-2">
        <NButton type="primary" @click="handleExport">
          <SvgIcon icon="uil:export"></SvgIcon>
          导出
        </NButton>
      </div>
    </template>
    <!-- 公司系数设置，隐藏菜单，只有通过路由才可以访问，菜单不展示 -->
    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="选择公司" :label-width="80">
        <n-select
          clearable
          v-model:value="queryItems.companyId"
          filterable
          placeholder="选择公司"
          :options="companyOptions"
        />
      </MeQueryItem>
      <MeQueryItem label="选择时间" :label-width="80">
        <n-date-picker 
          v-model:value="queryItems.time" 
          type="daterange" 
          value-format="yyyy-MM-dd" 
          clearable 
          style="width: 300px"
          />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="130" :model="modalForm"
        :disabled="modalAction === 'view'" :rules="rules">
        <n-form-item label="UV系数" path="uv">
          <n-input v-model:value="modalForm.uv" />
        </n-form-item>
        
        <n-form-item label="PV系数" path="pv">
          <n-input v-model:value="modalForm.pv" />
        </n-form-item>

        <n-form-item label="登录时间系数" path="loginTime">
          <n-input v-model:value="modalForm.loginTime" />
        </n-form-item>

        <n-form-item label="智能体点击系数" path="agentClick" >
          <n-input v-model:value="modalForm.agentClick" />
        </n-form-item>

        <n-form-item label="Token消耗总量" path="tokenUse">
          <n-input v-model:value="modalForm.tokenUse" />
        </n-form-item>

      </n-form>
    </MeModal>

  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem, FileUpload } from '@/components'
import { useCrud } from '@/composables'
import { NImage, NButton, NSwitch, NTag } from 'naive-ui'
import api from './api'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import { formatDate } from '@/utils'

import { ref } from 'vue'

const $table = ref(null)
const queryItems = ref({})

const rules = ref({
  pv:  {
    required: true,
    trigger: 'blur',
    message: '请输入PV系数',
  },
  uv:  {
    required: true,
    trigger: 'blur',
    message: '请输入UV系数',
  },
  loginTime:  {
    required: true,
    trigger: 'blur',
    message: '请输入登录时间系数',
  },
  agentClick:  {
    required: true,
    trigger: 'blur',
    message: '请输入智能体点击系数',
  },
  tokenUse:  {
    required: true,
    trigger: 'blur',
    message: '请输入Token消耗总量',
  },
})

let companyOptions = ref([])
const getCompanyList = async () => {
  let { data } = await api.companyList()
  companyOptions.value = data.map(item => {
    return {
      label: item.name,
      value: item.id
    }
  })
}

onMounted(() => {
  getCompanyList()
  $table.value?.handleSearch()
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleEdit,
} = useCrud({
  name: '公司系数',
  initForm: { enable: true, icon: '' },
  doUpdate: api.update,
  refresh: () => $table.value?.handleSearch(),
})


const columns = [
  { title: '公司名', key: 'companyName', width: 150, ellipsis: { tooltip: true } },
  { title: 'UV系数', key: 'uv', width: 150, ellipsis: { tooltip: true } },
  { title: 'PV系数', key: 'pv', width: 150, ellipsis: { tooltip: true } },
  { title: '登录时间系数', key: 'loginTime', width: 150, ellipsis: { tooltip: true } },
  { title: '智能体点击系数', key: 'agentClick', width: 150, ellipsis: { tooltip: true } },
  { title: 'Token消耗总量', key: 'tokenUse', width: 150, ellipsis: { tooltip: true } },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            style: 'margin-left: 12px;',
            onClick: () => {
              let recode = {}
              Object.keys(row).forEach(key => {
                recode[key] = row[key] + ''
              })
              handleEdit(recode)
            }
          },
          {
            default: () => '编辑',
            icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
          },
        ),
      ]
    },
  },
]

// 用户导出
const handleExport = () => {
  let params = {
    companyId: queryItems.value.companyId,
    startDate: queryItems.value.time ? formatDate(queryItems.value.time[0]) : null,
    endDate: queryItems.value.time ? formatDate(queryItems.value.time[1]) : null,
  }
  api.export(params).then((data) => {
    const url = window.URL.createObjectURL(new Blob([data]))
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', 'accounts.xlsx')
    document.body.appendChild(link)
    link.click()
  })
}

</script>
<style lang="scss" scoped>
.icon-box {
  .mask {
    display: none;
  }

  &:hover {
    .mask {
      display: flex;
    }
  }
}
</style>
