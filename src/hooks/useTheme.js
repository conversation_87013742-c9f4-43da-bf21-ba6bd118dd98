import { computed, watch } from "vue"
import { darkTheme, useOsTheme } from "naive-ui"
import { useAppStore } from "@/store"

export function useTheme() {
  const appStore = useAppStore()
  const OsTheme = useOsTheme()

  const isDark = computed(() => {
    return false
    // if (appStore.theme === "auto") return OsTheme.value === "dark"
    // else return appStore.theme === "dark"
  })

  const theme = computed(() => {
    // return isDark.value ? darkTheme : undefined
    return undefined
  })

  const themeOverrides = computed(() => {
    // if (isDark.value) {
    //   return {
    //     common: {
    //       // primaryColor: "#eeeeee",
    //       // primaryColorHover: "#ffffff",
    //     },
    //   }
    // }
    return {
      common: {
        baseColor: "#fcfcfc",
        primaryColor: "#467bff",
        primaryColorHover: "#85b1fd",
        textColorHover: "#85b1fd",
      },
    }
  })

  watch(
    () => isDark.value,
    (dark) => {
      document.documentElement.classList.remove("dark")
      // if (dark) document.documentElement.classList.add("dark")
      // else document.documentElement.classList.remove("dark")
    },
    { immediate: true }
  )

  return { theme, themeOverrides }
}
