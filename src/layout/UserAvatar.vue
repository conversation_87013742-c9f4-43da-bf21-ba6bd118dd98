<template>
  <n-dropdown v-if="!appStore.isPreview" :options="options" @select="handleSelect">
    <div id="user-dropdown" class="flex cursor-pointer items-center">
      <n-avatar class="shrink-0" round :size="36" :src="userStore.avatar" />
      <div v-if="userStore.userInfo" class="ml-3 flex flex-col flex-shrink-0 items-center">
        <span class="text-14">{{ userStore.userInfo.nickName || userStore.userInfo.username }}</span>
      </div>
    </div>
  </n-dropdown>

  <div v-if="appStore.getIsPreview" class="cursor-pointer border rounded-lg py-1 px-2 hover:bg-[#ECF1FF]"
    @click="exitPreview">
    退出预览
  </div>


  <Password ref="passwordRef"></Password>
  <!-- <RoleSelect ref="roleSelectRef" /> -->
</template>

<script setup>
// import api from '@/api'
// import { RoleSelect } from '@/layouts/components'
import { useAuthStore, useAppStore, usePermissionStore, useUserStore } from '@/store'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import Password from './Password.vue'
import { reactive, computed } from 'vue'


const router = useRouter()
const userStore = useUserStore()
const authStore = useAuthStore()
const appStore = useAppStore()
const permissionStore = usePermissionStore()

const avatar = computed(() => userStore.avatar)

const passwordRef = ref(null)
const options = reactive([
  {
    label: '修改密码',
    key: 'password',
    icon: () => h(SvgIcon, { icon: 'mingcute:edit-line' }),
    // show: computed(() => permissionStore.accessRoutes?.some(item => item.path === '/profile')),
  },
  // {
  //   label: '切换角色',
  //   key: 'toggleRole',
  //   icon: () => h(SvgIcon, { icon: 'icon-park-outline:switch' }),
  //   // show: computed(() => userStore.roles.length > 1),
  // },
  {
    label: '退出登录',
    key: 'logout',
    icon: () => h(SvgIcon, { icon: 'mdi:logout' }),
  },
])

const roleSelectRef = ref(null)
function handleSelect(key) {
  switch (key) {
    case 'password':
      passwordRef.value?.show()
      break
    case 'toggleRole':
      roleSelectRef.value?.open({
        onOk() {
          location.reload()
        },
      })
      break
    case 'logout':
      $dialog.confirm({
        title: '提示',
        type: 'info',
        content: '确认退出？',
        async confirm() {
          try {
            await api.logout()
          }
          catch (error) {
            console.error(error)
          }
          authStore.logout()
          $message.success('已退出登录')
        },
      })
      break
  }
}

const exitPreview = () => {
  // appStore.setLoading({
  //   showLoading: true,
  //   loadingText: '退出预览模式'
  // })
  appStore.setPreviewCompanyId('-1')
  appStore.setPreviewCompanyLogo('')
  router.push({ path: '/agents' })
}
</script>
