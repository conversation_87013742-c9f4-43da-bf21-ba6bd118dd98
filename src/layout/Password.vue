<template>
  <n-modal v-model:show="modalShow" title="修改密码" preset="dialog" size="huge" :bordered="false" class="overflow-hidden"
    :show-icon="false">

    <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
      :rules="rules">
      <n-form-item label="原密码" path="oldPassword">
        <n-input v-model:value="modalForm.oldPassword" placeholder="请输入原密码" />
      </n-form-item>
      <n-form-item label="新密码" path="newPassword">
        <n-input v-model:value="modalForm.newPassword" placeholder="请输入新密码" :maxlength="8"/>
      </n-form-item>
      <n-form-item label="确认密码" path="confirmPassword">
        <n-input v-model:value="modalForm.confirmPassword" placeholder="请输入确认密码" :maxlength="8"/>
      </n-form-item>
    </n-form>
    <div class="flex justify-end items-center gap-2">
      <n-button @click="close()">取消</n-button>
      <n-button type="primary" @click="submit()">确定</n-button>
    </div>
  </n-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import api from '@/api'
import { useRouter } from 'vue-router'

const router = useRouter()
const modalShow = ref(false)
const modalForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const rules = reactive({
  oldPassword: {
    required: true,
    trigger: 'blur',
    message: '请输入旧密码',
  },
  newPassword: {
    required: true,
    validator: (_, value) => {
      if (value === '') {
        return new Error('请输入确认密码')
      }
      // 密码必须为字母数字符号，长度 8 位
      if (!/^[a-zA-Z0-9@#$%&*^_-]{8,}$/.test(value)) {
        return new Error('密码必须为字母数字，长度 8 位')
      }
      return true
    },
    trigger: ['blur'],
  },
  confirmPassword: {
    required: true,
    validator: (_, value) => {
      if (value === '') {
        return new Error('请输入确认密码')
      }
      if (value !== modalForm.value.newPassword) {
        return new Error('两次密码不一致')
      }
      // 密码必须为字母数字符号，长度 8 位
      if (!/^[a-zA-Z0-9@#$%&*^_-]{8,}$/.test(value)) {
        return new Error('密码必须为字母数字，长度 8 位')
      }
      return true
    },
    trigger: ['blur'],
  },
})
const close = () => {
  modalShow.value = false
}

const show = () => {
  modalShow.value = true
}


const modalFormRef = ref(null)
const submit = () => {
  modalFormRef.value.validate((errors) => {
    if (!errors) {
      api.updatePassword(modalForm.value).then(() => {
        modalShow.value = false
        $message.success('修改成功, 请重新登录')
        setTimeout(() => {
          router.push('/login')
        }, 1000)
      })
    }
  })
}
defineExpose({
  show,
  close,
})
</script>