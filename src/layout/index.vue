<template>
  <!-- login 不需要 layout -->
  <router-view v-if="fullViewPagePath.includes(route.path)"></router-view>
  <n-layout v-else class="h-full w-full">
    <n-layout-header>
      <Header></Header>
    </n-layout-header>
    <div class="main-container bg-[#F9FAFE]">
      <!-- <component v-if="isAdmin" :is="AdminLayout">
        <transition name="fade-slide" mode="out-in" appear>
          <RouterView></RouterView>
        </transition>
      </component> -->
      <!-- <router-view v-else></router-view> -->
      <router-view></router-view>
    </div>
  </n-layout>

</template>

<script setup>
import { computed, onUnmounted } from "vue";
import Header from "./header.vue"
import AdminLayout from "./admin/index.vue"
import { useAppStore, useAgentStore } from "@/store";
import { onMounted } from "vue"
import { useRoute } from "vue-router";
import api from '@/api'

const appStore = useAppStore();
const agentStore = useAgentStore()
const route = useRoute();

// 不加载layout布局路由
const fullViewPagePath = ['/login',  '/sso']

onMounted(() => {
  agentStore.setCurrentAgent(undefined)
  initSideMenus()
  // heartBeat()
})

watch(() => appStore.previewCompanyId, (val) => {
  if (val === '-1') exitPreview()
  else if (val) {
    agentStore.initPreview(appStore.previewCompanyId)
    enterPreview()
  }
})

const initSideMenus = async () => {
  agentStore.initGroupList()
}

const enterPreview = () => {
  appStore.setLoading({
    showLoading: true,
    loadingText: '预览模式加载中'
  })

  // 查询智能体信息
  setTimeout(() => {
    // appStore.setPreviewCompanyId('')
    appStore.setLoading({
      showLoading: false,
    })
  }, 2000)
}
const exitPreview = () => {
  appStore.setLoading({
    showLoading: true,
    loadingText: '退出预览模式'
  })
  appStore.setPreviewCompanyLogo('')
  appStore.setPreviewCompanyId('')

  // 查询智能体信息
  setTimeout(() => {
    // appStore.setPreviewCompanyId('')
    appStore.setLoading({
      showLoading: false,
    })
  }, 2000)
  setTimeout(() => {
    initSideMenus()
  }, 1000)
}

// 记录用户使用时长，采用60s调动一次
let intervalId = ref(null);
const heartBeat = () => {
  setServerHearBeeat()
  intervalId.value = setInterval(() => {
    setServerHearBeeat()
  }, 60000)
}

const stopHeartBeat = () => {
  clearInterval(intervalId.value)
}

const setServerHearBeeat = async () => {
  api.getHeartbeatApi()
}

onUnmounted(() => {
  stopHeartBeat()
})

defineProps({})
</script>


<style scoped>
.main-container {
  height: calc(100vh - 56px);
}
</style>
