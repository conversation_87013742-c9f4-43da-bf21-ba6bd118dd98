<template>
  <div class="h-[56px] pl-6 pr-8 flex items-center justify-between border">
    <div class="left flex items-center ">
      <div class="logo w-[450px] flex items-center justify-center">
        <n-image class="max-w-[150px] h-[40px]" object-fit="contain" :src="logo" />
        <h3 class="logo_title">全国最大的省级运营商中文知识库</h3>
      </div>
      <div class="nav-bar flex items-center gap-[10px] ml-[60px]">
        <!-- permissionStore.menus -->
        <template v-for="(item, index) in permissionStore.menus" :key="index">
          <!-- 预览模式下，只显示agents -->
          <div v-if="!appStore.getIsPreview || item.path == '/agents'"
            class="item flex items-center text-sm font-bold px-[10px] py-[7px] cursor-pointer rounded-full	shrink-0"
            :class="[isActive(item) ? 'item-active text-white' : 'hover:text-[#467bff]']" @click="menuClick(item)">
            <!-- 普通一级菜单 -->
            <div v-if="!item.isLink" class="flex items-center">
              <SvgIcon :icon="item.icon" :width="20" :height="20">
              </SvgIcon>
              <span class="item-text ml-[6px]">{{ item.label }}</span>
            </div>
          </div>
          <!-- link 菜单 -->
          <template v-if="item.path == '/agents' && agentStore.linkList?.length > 0">
            <template v-for="(linkItem, index) in agentStore.linkList">
              <!-- 单级 link -->
              <div v-if="linkItem.agents.length == 1"
                class="item flex items-center text-sm font-bold px-[10px] py-[7px] cursor-pointer gap-1 rounded-full	shrink-0"
                :class="[isActive(linkItem) ? 'item-active text-white' : 'hover:text-[#467bff]']"
                @click="menuClick(linkItem)">
                <SvgIcon v-if="linkItem.icon" :icon="linkItem.icon" :width="20" :height="20">
                </SvgIcon>
                <span class="item-text"> {{ linkItem.name }}</span>
              </div>
              <!-- 多级 link -->
              <n-dropdown v-if="linkItem.agents.length > 1" trigger="hover" :options="linkItem.agents"
                label-field="name" key-field="id" @select="agentMenuSelect">
                <div
                  class="flex items-center text-sm font-bold px-[10px] py-[7px] cursor-pointer gap-1 rounded-full	shrink-0"
                  :class="[isActive(linkItem) ? 'item-active text-white' : 'hover:text-[#467bff]']">
                  <SvgIcon v-if="linkItem.icon" :icon="linkItem.icon" :width="20" :height="20">
                  </SvgIcon>
                  <span class="item-text cursor-pointer">{{ linkItem.name }}</span>
                </div>
              </n-dropdown>
            </template>
          </template>
        </template>
      </div>
    </div>
    <div class="right flex items-center gap-2">
      <!-- <n-button type="primary" size="small" @click="router.push('/login')">登录</n-button> -->
      <UserAvatar></UserAvatar>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { navList } from '@/utils/constant';
import { useRoute, useRouter } from 'vue-router'
import UserAvatar from './UserAvatar.vue'
import { usePermissionStore } from '@/store/modules/permission'
import { useUserStore, useAgentStore, useAppStore } from '@/store'
import LogoImg from '@/assets/images/logo.png'

const route = useRoute()
const router = useRouter()
const permissionStore = usePermissionStore()
const userStore = useUserStore()
const agentStore = useAgentStore()
const appStore = useAppStore()

const logo = computed(() => {
  return appStore.previewCompanyLogo || LogoImg
})


// 智能体大厅
const isAgentHall = computed(() => {
  return route.path === '/agents'
})


const currentLink = ref()
const menuClick = (item) => {
  if (item.linkId) {
    // 处理快捷菜单
    console.log(item);
    currentLink.value = item
    agentStore.setCurrentAgent(item)
    if (!isAgentHall.value) {
      router.push('/agents')
    }
  } else {
    agentStore.setCurrentAgent(undefined)
    currentLink.value = null
    router.push(item.path)
  }
  console.log(permissionStore.menus)
}

// 分组中的菜单
const agentMenuSelect = (_, item) => {
  // 设置 菜单
  currentLink.value = agentStore.linkList.find(link => link.linkId == item.linkId)
  agentStore.setCurrentAgent(item)
  if (!isAgentHall.value) {
    router.push('/agents')
  }
}

// 接受参数的 computed
const isActive = computed(() => {
  return (item) => {
    // link 和 menu 选中不同时
    if (currentLink.value) {
      //  TODO 一期不需要高亮，直接跳转新的 tab
      return currentLink.value?.linkId == item.linkId
    } else {
      return route.path.includes(item.path)
    }
  }
})

</script>

<style lang="scss" scoped>
.nav-bar .item-text {
  display: none;
}

// todo 此处宽度不稳定，不确定顶部导航数量，需要优化
@media (min-width: 1200px) {
  .nav-bar .item-text {
    display: block;
  }
}

.item-active {
  background-color: var(--dify_color_primary);
}
.logo_title {
  font-size: 18px;
  font-weight: bold;
  margin-left: 14px;
  color: #393939;
}
</style>
