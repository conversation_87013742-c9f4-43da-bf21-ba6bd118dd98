import { createApp } from 'vue'
import { setupDirectives } from '@/directives'

import './style.css'
import App from './App.vue'
import naive from 'naive-ui'
import { setupRouter } from './router'
// import stores from '@/store'
import { setupStore } from '@/store'
import { setupNaiveDiscreteApi } from '@/utils'
import "@/style/tailwind.css"
import "@/style/index.scss"
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import * as echarts from 'echarts';


async function bootstrap() {
  const app = createApp(App)
  app.use(naive)
  setupStore(app)
  setupDirectives(app)
  app.use(SvgIcon)
  await setupRouter(app)
  app.mount('#app')
  app.config.globalProperties.$echarts = echarts;
  setupNaiveDiscreteApi()
}

bootstrap()