// 需求类型枚举值
export const demandDirtionaryOptions = [
  { label: '需求类型', value: 'DEMAND' },
]

// 需求状态枚举值
export const demandStatusMap = {
  0: 'info',
  1: 'primary',
  2: 'success',
  3: 'error',
}
export const demandStatusOptions = [
  { label: '草稿', value: '0', tagType: 'info' },
  { label: '审核中', value: '1', tagType: 'primary' },
  { label: '通过', value: '2', tagType: 'success' },
  { label: '驳回', value: '3', tagType: 'danger' },
]

export const mapDirtionaryLabel = (value, options) => {
  let dirItem = options.find(item => {
    if (item.value === value) {
      return item
    }
  })
  if (!dirItem) return '-'
  return dirItem.label
}

// 根据value获取label
export const mapDirtionaryKey = (value, options) => {
  let dirItem = options.find(item => {
    if (item.key === value) {
      return item
    }
  })
  if (!dirItem) return '-'
  return dirItem.value
}

