import iconMap from "@/assets/icons/icon-map.json";

// 图标缓存
const iconCache = new Map();
let isInitialized = false;

/**
 * 预加载所有本地图标
 */
export async function preloadIcons() {
  if (isInitialized) return;
  
  console.log('开始预加载本地图标...');
  
  const iconNames = Object.keys(iconMap);
  const batchSize = 10; // 批量加载，避免过多并发请求
  
  for (let i = 0; i < iconNames.length; i += batchSize) {
    const batch = iconNames.slice(i, i + batchSize);
    
    await Promise.allSettled(
      batch.map(async (iconName) => {
        try {
          const [collection, name] = iconName.split(':');
          if (collection && name) {
            const response = await fetch(`/icons/${collection}/${name}.svg`);
            if (response.ok) {
              const svgContent = await response.text();
              if (svgContent && svgContent.includes('<svg')) {
                iconCache.set(iconName, svgContent);
              }
            }
          }
        } catch (error) {
          console.warn(`预加载图标失败: ${iconName}`, error);
        }
      })
    );
    
    // 添加小延迟避免请求过于频繁
    if (i + batchSize < iconNames.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  isInitialized = true;
  console.log(`预加载完成，成功加载 ${iconCache.size} 个图标`);
}

/**
 * 获取本地图标内容
 * @param {string} iconName - 图标名称，格式如 'ri:chat-ai-line'
 * @returns {string|null} SVG 内容或 null
 */
export function getLocalIcon(iconName) {
  return iconCache.get(iconName) || null;
}

/**
 * 检查图标是否存在于本地映射中（不管是否已缓存）
 * @param {string} iconName - 图标名称
 * @returns {boolean}
 */
export function hasLocalIcon(iconName) {
  return iconName in iconMap;
}

/**
 * 异步获取图标内容（如果缓存中没有则尝试加载）
 * @param {string} iconName - 图标名称
 * @returns {Promise<string|null>}
 */
export async function getIconAsync(iconName) {
  // 先检查缓存
  if (iconCache.has(iconName)) {
    return iconCache.get(iconName);
  }
  
  // 如果缓存中没有，尝试加载
  if (iconMap[iconName]) {
    try {
      const [collection, name] = iconName.split(':');
      if (collection && name) {
        const response = await fetch(`/icons/${collection}/${name}.svg`);
        if (response.ok) {
          const svgContent = await response.text();
          if (svgContent && svgContent.includes('<svg')) {
            iconCache.set(iconName, svgContent);
            return svgContent;
          }
        }
      }
    } catch (error) {
      console.warn(`加载图标失败: ${iconName}`, error);
    }
  }
  
  return null;
}

/**
 * 获取缓存统计信息
 */
export function getCacheStats() {
  return {
    totalIcons: Object.keys(iconMap).length,
    cachedIcons: iconCache.size,
    isInitialized
  };
}
