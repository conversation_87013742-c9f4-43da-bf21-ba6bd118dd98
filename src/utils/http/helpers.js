
import { useAuthStore } from '@/store'
import api from '@/api'

let isConfirming = false
export async function resolveResError(code, message, needTip = true) {
  switch (+code) {
    case 401:
      // 跳转登录
      useAuthStore().logout()
      return false
    case 402:
      // TODO 需要刷新 token 调用 freshToken
      const res = await api.refresh_token()
      useAuthStore().setToken(res.data)
      // if (isConfirming || !needTip)
      //   return
      // isConfirming = true
      // $dialog.confirm({
      //   title: '提示',
      //   type: 'info',
      //   content: '登录已过期，是否重新登录？',
      //   confirm() {
      //     useAuthStore().logout()
      //     window.$message?.success('已退出登录')
      //     isConfirming = false
      //   },
      //   cancel() {
      //     isConfirming = false
      //   },
      // })
      return false
    case 11007:
    case 11008:
      if (isConfirming || !needTip)
        return
      isConfirming = true
      $dialog.confirm({
        title: '提示',
        type: 'info',
        content: `${message}，是否重新登录？`,
        confirm() {
          useAuthStore().logout()
          window.$message?.success('已退出登录')
          isConfirming = false
        },
        cancel() {
          isConfirming = false
        },
      })
      return false
    case 403:
      message = '请求被拒绝'
      break
    case 404:
      message = '请求资源或接口不存在'
      break
    // case 500:
    //   message = '服务器发生异常'
      break
    default:
      message = message ?? `【${code}】: 未知异常!`
      break
  }
  needTip && window.$message?.error(message)
  return message
}
