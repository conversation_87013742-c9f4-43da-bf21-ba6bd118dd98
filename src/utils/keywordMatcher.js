/**
 * 关键词匹配模块
 * 用于处理特定关键词的自动回复功能
 */

// 关键词匹配配置
const keywordResponses = [
  {
    keywords: ['4A系统所有欧拉操作系统的主机和数据库主机要做升级改造'],
    response: `您好！已收到您关于7月31日4A系统欧拉操作系统及数据库主机升级改造的网络安全"三
同步"（同步规划、同步建设、同步运行）需求。经查询资产库，4A系统升级涉及资产IP
清单如下：
请确认资产清单是否准确？`
  },
  {
    keywords: ['准确的，请帮我于', '进行网络安全三同步操作'],
    response: `根据《中国移动网络安全三同步管理办法》要求，以下是具体执行计划：

**1. 渗透测试**
- 时间节点：2025年7月31日4:30
- 执行内容：邮件通知安服部门进行渗透测试，并输出报告

**2. 漏洞扫描**
- 时间节点：2025年7月31日4:32
- 执行内容：调用绿盟扫描器对资产进行漏扫并输出报告，如有风险资产通过EOMS派发工单

**3. 安全设备有效性验证**
- 时间节点：2025年7月31日4:35
- 执行内容：调用BAS对资产进行有效性验证并输出报告

请确认是否要执行以上计划？`
  },
  {
    keywords: ['请帮我执行计划', '并将汇总结果发我'],
    response: `好的，根据您的需求正在执行计划1、2、3

      **1. 执行渗透测试剧本，输出报告**

      **2. 执行漏洞扫描剧本，输出报告**
      漏洞扫描已完成
      经漏扫发现有风险资产，已派发工单
      工单ID: AH-074-250731-12

      **3. 执行安全设备有效性验证剧本，输出报告**

      **汇总结果如下：**
      详细信息请查看各项报告，如有疑问请联系安全运维团队。`,
    needsAIProcessing: true // 标记需要AI进一步处理
  }
]

/**
 * 检测消息是否匹配关键词
 * @param {string} message - 用户输入的消息
 * @returns {Object|null} 匹配的配置对象，如果没有匹配则返回 null
 */
export function matchKeywords(message) {
  if (!message || typeof message !== 'string') {
    return null
  }

  for (const config of keywordResponses) {
    const hasMatch = config.keywords.some(keyword =>
      message.includes(keyword)
    )
    if (hasMatch) {
      return config
    }
  }
  return null
}

/**
 * 检测是否为处置消息
 * @param {string} message - 用户输入的消息
 * @returns {boolean} 是否为处置消息
 */
export function isDisposalMessage(message) {
  if (!message || typeof message !== 'string') {
    return false
  }
  return message.startsWith('处置：')
}

/**
 * 获取所有关键词配置（用于调试或管理）
 * @returns {Array} 关键词配置数组
 */
export function getKeywordConfigs() {
  return [...keywordResponses] // 返回副本，避免外部修改
}

/**
 * 添加新的关键词配置
 * @param {Object} config - 新的关键词配置
 * @param {Array<string>} config.keywords - 关键词数组
 * @param {string} config.response - 回复内容
 * @param {boolean} [config.needsAIProcessing] - 是否需要AI进一步处理
 */
export function addKeywordConfig(config) {
  if (!config || !config.keywords || !config.response) {
    throw new Error('关键词配置格式错误')
  }
  
  if (!Array.isArray(config.keywords) || config.keywords.length === 0) {
    throw new Error('关键词必须是非空数组')
  }
  
  if (typeof config.response !== 'string' || config.response.trim() === '') {
    throw new Error('回复内容不能为空')
  }
  
  keywordResponses.push({
    keywords: [...config.keywords], // 创建副本
    response: config.response,
    needsAIProcessing: Boolean(config.needsAIProcessing)
  })
}

/**
 * 移除关键词配置
 * @param {number} index - 要移除的配置索引
 */
export function removeKeywordConfig(index) {
  if (index >= 0 && index < keywordResponses.length) {
    keywordResponses.splice(index, 1)
  }
}

/**
 * 清空所有关键词配置
 */
export function clearKeywordConfigs() {
  keywordResponses.length = 0
}

/**
 * 检查是否启用关键词匹配功能
 * 可以通过环境变量或配置文件控制
 * @returns {boolean} 是否启用关键词匹配
 */
export function isKeywordMatchingEnabled() {
  // 可以通过环境变量控制
  if (typeof window !== 'undefined' && window.DISABLE_KEYWORD_MATCHING) {
    return false
  }
  
  // 默认启用
  return true
}

/**
 * 主要的关键词处理函数
 * 这是外部调用的主要接口
 * @param {string} message - 用户输入的消息
 * @returns {Object|null} 处理结果
 */
export function processKeywordMessage(message) {
  // 检查功能是否启用
  if (!isKeywordMatchingEnabled()) {
    return null
  }
  
  // 检查是否为处置消息
  if (isDisposalMessage(message)) {
    return {
      type: 'disposal',
      message: message
    }
  }
  
  // 检查关键词匹配
  const keywordMatch = matchKeywords(message)
  if (keywordMatch) {
    return {
      type: 'keyword',
      config: keywordMatch,
      message: message
    }
  }
  
  return null
}
