/**
 * 关键词匹配模块测试
 * 用于验证关键词匹配功能是否正常工作
 */

import { 
  matchKeywords, 
  isDisposalMessage, 
  processKeywordMessage,
  getKeywordConfigs,
  addKeywordConfig,
  isKeywordMatchingEnabled
} from './keywordMatcher.js'

// 测试关键词匹配
function testKeywordMatching() {
  console.log('=== 测试关键词匹配 ===')
  
  // 测试第一个关键词
  const result1 = matchKeywords('4A系统所有欧拉操作系统的主机和数据库主机要做升级改造')
  console.log('测试1:', result1 ? '✓ 匹配成功' : '✗ 匹配失败')
  
  // 测试第二个关键词
  const result2 = matchKeywords('准确的，请帮我于今天进行网络安全三同步操作')
  console.log('测试2:', result2 ? '✓ 匹配成功' : '✗ 匹配失败')
  
  // 测试第三个关键词
  const result3 = matchKeywords('请帮我执行计划并将汇总结果发我')
  console.log('测试3:', result3 ? '✓ 匹配成功' : '✗ 匹配失败')
  
  // 测试不匹配的消息
  const result4 = matchKeywords('这是一个普通的消息')
  console.log('测试4:', !result4 ? '✓ 正确不匹配' : '✗ 错误匹配')
  
  console.log('')
}

// 测试处置消息检测
function testDisposalMessage() {
  console.log('=== 测试处置消息检测 ===')
  
  const result1 = isDisposalMessage('处置：这是一个处置消息')
  console.log('测试1:', result1 ? '✓ 检测成功' : '✗ 检测失败')
  
  const result2 = isDisposalMessage('这不是处置消息')
  console.log('测试2:', !result2 ? '✓ 正确不匹配' : '✗ 错误匹配')
  
  console.log('')
}

// 测试主处理函数
function testProcessKeywordMessage() {
  console.log('=== 测试主处理函数 ===')
  
  // 测试处置消息
  const result1 = processKeywordMessage('处置：测试处置')
  console.log('测试1:', result1?.type === 'disposal' ? '✓ 处置消息处理成功' : '✗ 处置消息处理失败')
  
  // 测试关键词匹配
  const result2 = processKeywordMessage('4A系统所有欧拉操作系统的主机和数据库主机要做升级改造')
  console.log('测试2:', result2?.type === 'keyword' ? '✓ 关键词匹配成功' : '✗ 关键词匹配失败')
  
  // 测试普通消息
  const result3 = processKeywordMessage('普通消息')
  console.log('测试3:', !result3 ? '✓ 普通消息正确处理' : '✗ 普通消息处理错误')
  
  console.log('')
}

// 测试配置管理
function testConfigManagement() {
  console.log('=== 测试配置管理 ===')
  
  const configs = getKeywordConfigs()
  console.log('当前配置数量:', configs.length)
  
  // 测试添加配置
  try {
    addKeywordConfig({
      keywords: ['测试关键词'],
      response: '测试回复',
      needsAIProcessing: false
    })
    console.log('✓ 添加配置成功')
  } catch (error) {
    console.log('✗ 添加配置失败:', error.message)
  }
  
  // 测试无效配置
  try {
    addKeywordConfig({
      keywords: [],
      response: '测试回复'
    })
    console.log('✗ 应该抛出错误但没有')
  } catch (error) {
    console.log('✓ 正确拒绝无效配置:', error.message)
  }
  
  console.log('')
}

// 测试功能开关
function testFeatureToggle() {
  console.log('=== 测试功能开关 ===')
  
  const enabled = isKeywordMatchingEnabled()
  console.log('功能状态:', enabled ? '启用' : '禁用')
  
  console.log('')
}

// 运行所有测试
function runAllTests() {
  console.log('开始运行关键词匹配模块测试...\n')
  
  testKeywordMatching()
  testDisposalMessage()
  testProcessKeywordMessage()
  testConfigManagement()
  testFeatureToggle()
  
  console.log('测试完成！')
}

// 如果直接运行此文件
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.testKeywordMatcher = runAllTests
  console.log('测试函数已添加到 window.testKeywordMatcher，请在控制台中调用')
} else {
  // Node.js 环境
  runAllTests()
}

export { runAllTests }
