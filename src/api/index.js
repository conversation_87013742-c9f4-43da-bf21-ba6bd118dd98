/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/04 22:50:38
 * @Email: <EMAIL>
 * Copyright © 2023 Ronnie <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { request } from '@/utils'

export default {
  // 获取用户信息
  getUser: () => request.get('/user/detail'),

  getUserById: () => request.get('/v2/system/user/getById'),

  // 刷新token
  refresh_token: () => request.post('/v2/refreshToken', { refreshToken: localStorage.getItem('refresh_token') }),
  // 登出
  logout: () => request.post('/auth/logout', {}, { needTip: false }),
  // 切换当前角色
  switchCurrentRole: role => request.post(`/auth/current-role/switch/${role}`),
  // 获取角色权限
  // getRolePermissions: () => request.get('/role/permissions/tree'),
  // 验证菜单路径
  validateMenuPath: path => request.get(`/permission/menu/validate?path=${path}`),


  // 获取角色权限
  getRolePermissions: () => request.get('/v2/system/menu/menuTree', { params: {} }),
  // getRolePermissions: (data = {}) => request.post('/v2/system/menu/list', data),
  linkList: (data = {}) => request.post('/v2/system/link/list', data),
  // 文件上传 系统：system-file  需求：demand-file
  uploadFile: data => request.post(`/v2/system/upload`, data),
  // 文件下载 系统：system-file  需求：demand-file
  downloadFile: (params) => request.get(`/v2/system/download`, { params, responseType: 'blob' }),
  // 获取代理商列表
  getAllAgents: (data = {}) => request.post('/v2/system/agent/list', data),
  // 获取大厅列表
  getHallList: () => request.get('/v2/system/agent/hallList', { params: {} }),
  // 收藏的智能体
  collectionAgent: (agentId) => request.get('/v2/system/agent/collection', { params: { agentId } }),
  // /v2/system/agent/cancelCollection
  cancelCollectionAgent: (agentId) => request.get('/v2/system/agent/cancelCollection', { params: { agentId } }),
  // 获取收藏的智能体
  getCollectionAgents: () => request.get('/v2/system/agent/collectionList', { params: {} }),

  // /v2/system/company/getById
  getCompanyById: (companyId) => request.get('/v2/system/company/getById', { params: { companyId } }),

  // 预览
  getAgentPreview: (companyId) => request.get('/v2/system/agent/agentPreview', { params: { companyId } }),

  // 获取字典列表
  dictionaryList: (data = {}) => request.post('/v2/system/dictionary/list', data),

  updatePassword: (data = {}) => request.post('/v2/system/user/updatePassword', data),

  // 用户使用时长心跳
  getHeartbeatApi: () => request.get('/v2/system/dashboard/heartbeat', { params: {} }),
  // 用户使用智能体次数统计
  getStatisticTimesApi: (agentId) => request.get('/v2/system/dashboard/statisticTimes', { params: { agentId } }),
  
}
