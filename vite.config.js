import { defineConfig, loadEnv } from "vite"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import path from "path"
import vue from "@vitejs/plugin-vue"
import tailwindcss from "tailwindcss"
import autoprefixer from "autoprefixer"
import { NaiveUiResolver } from "unplugin-vue-components/resolvers"
import { pluginIcons, pluginPagePathes } from './build/plugin-isme'
const env = loadEnv(process.cwd(), '')

export default defineConfig({
  plugins: [
    AutoImport({
      imports: [
        "vue",
        'vue-router',
        {
          "naive-ui": [
            "useDialog",
            "useMessage",
            "useNotification",
            "useLoadingBar",
          ],
        },
      ],
    }),
    Components({
      resolvers: [NaiveUiResolver()],
    }),
    // 自定义插件，用于生成页面文件的path，并添加到虚拟模块
    pluginPagePathes(),
    vue(),
  ],
  css: {
    postcss: {
      plugins: [tailwindcss, autoprefixer],
    },
  },
  server: {
    host: "0.0.0.0",
    port: 8020,
    proxy: {
      "/v2": {
        target: env.VITE_BASE_API, // "http://*************:6020/",
        // target: "/",
        changeOrigin: true,
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"), // 假设你的源代码在 'src' 目录下
    },
  },
  envDir: "./",
  // build: {
  //   rollupOptions: {
  //     output: {
  //       assetFileNames: (assetInfo) => {
  //         return 'system/assets/[name].[hash][extname]';
  //       },
  //       chunkFileNames: 'system/assets/[name].[hash].js',
  //       entryFileNames: 'system/assets/[name].[hash].js',
  //     },
  //   },
  // },
})
